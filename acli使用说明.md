# ACLI (Atlassian CLI) 工具使用说明

**ACLI** 是 Atlassian 公司提供的命令行工具，让您可以从命令行无缝地使用 Atlassian 的各种服务。

## 功能模块对比

| 功能模块 | 主要用途 | 目标用户 | 核心功能 |
|---------|---------|---------|---------|
| **jira** | 项目管理 | 开发团队、项目经理 | 创建项目、管理工作项、跟踪进度 |
| **admin** | 组织管理 | 系统管理员 | 用户管理、权限控制、账户操作 |
| **rovodev** | AI编程助手 | 开发者 | 代码生成、重构、调试、测试 |

## 主要功能模块

### 1. jira - Jira Cloud 命令
**jira** 命令提供完整的 Jira 项目管理功能，包括项目和工作项的全生命周期管理。

#### 认证管理 (`jira auth`)
- 用于 Jira Cloud 的身份验证和账户管理

#### 项目管理 (`jira project`)
- **`create`** - 创建新的 Jira 项目
- **`list`** - 列出用户可见的所有项目
- **`view`** - 查看特定项目的详细信息
- **`update`** - 更新项目设置和配置
- **`archive`** - 归档不再使用的项目
- **`restore`** - 恢复已归档的项目
- **`delete`** - 永久删除项目

#### 工作项管理 (`jira workitem`)
- **`create`** - 创建新的工作项（任务、Bug、故事等）
- **`search`** - 搜索和筛选工作项
- **`view`** - 查看工作项详细信息
- **`edit`** - 编辑工作项内容
- **`assign`** - 分配工作项给团队成员
- **`transition`** - 转换工作项状态（如：待办→进行中→完成）
- **`comment`** - 为工作项添加评论
- **`clone`** - 复制现有工作项
- **`archive/unarchive`** - 归档/取消归档工作项
- **`delete`** - 删除工作项

#### 其他功能
- **`dashboard`** - 仪表板管理命令
- **`filter`** - 过滤器管理命令

### 2. admin - 管理员命令
**admin** 命令是专门为 **Atlassian 组织管理员** 设计的高级管理功能，用于管理整个 Atlassian 组织的用户和权限。

#### 认证管理 (`admin auth`)
- **功能说明**：专门针对组织管理员级别的认证，权限比普通用户更高
- **`login`** - 以组织管理员身份登录认证
- **`logout`** - 退出组织管理员账户登录
- **`status`** - 查看当前组织管理员账户状态
- **`switch`** - 在多个 Atlassian 组织管理员账户之间切换

#### 用户管理 (`admin user`)
- **`activate`** - 激活用户（恢复被停用用户的访问权限）
- **`deactivate`** - 停用用户（暂时禁用访问权限，但保留账户数据）
- **`delete`** - 删除托管账户（⚠️ 永久删除，不可逆操作）
- **`cancel-delete`** - 取消删除托管账户（撤销删除操作）

#### 使用示例
```bash
# 管理员登录
.\acli.exe admin auth login

# 停用离职员工账户
.\acli.exe admin user deactivate --user "<EMAIL>"

# 激活新员工账户
.\acli.exe admin user activate --user "<EMAIL>"
```

#### 权限级别说明
- **普通用户权限**：只能管理自己的任务、项目参与等
- **组织管理员权限**：可以管理整个组织的用户、设置、权限等

### 3. rovodev - Atlassian 的 AI 编程助手 (Beta 版本)
- 这是一个 AI 驱动的编程代理，可以帮助您进行代码开发

## Rovo Dev 详细功能

**Rovo Dev** 是这个工具的核心 AI 编程助手，包含以下子命令：

### 认证相关 (`auth`)
- `login` - 登录认证以使用 Rovo Dev
- `logout` - 退出登录
- `status` - 查看当前认证状态

### 运行 AI 助手 (`run`)
```bash
acli rovodev run [选项] [消息...]
```

**核心功能：** 启动 Rovo Dev AI 编程助手，这是一个智能代码助手，能理解自然语言指令并执行相应的编程任务。

#### 参数说明

**消息参数 `[MESSAGE]...`：**
- 给 AI 助手的初始指令，使用自然语言描述需求
- 可以是单个指令或多个词组成的复杂指令
- 如果不提供，启动后可以交互式输入

#### 重要选项详解

**`--shadow` (影子模式) - 强烈推荐**
- 在临时克隆的工作空间中运行 AI 助手
- 所有更改都在临时环境中进行，不直接修改源代码
- 完成后提示是否应用更改到真实工作空间
- 特别适合重要项目或不确定操作结果时使用

**`--verbose` (详细模式) - 强烈推荐用于学习**
- 显示 AI 助手使用的所有工具和详细操作过程
- 实时显示工具调用：如 `open_files`、`expand_code_chunks`、`run_command` 等
- 用于学习 AI 工作方式、调试问题、了解每步操作
- 透明化 AI 的决策过程和任务分解方式
- 帮助用户理解 AI 如何与代码库交互

**`--restore` (恢复会话)**
- 继续上次未完成的对话会话
- 适用于意外中断后继续工作、保持上下文连续性

**`--config-file`**
- 指定配置文件路径 (默认: `C:\Users\<USER>\.rovodev\config.yml`)
- 自定义 AI 助手的行为设置

#### AI 助手功能范围
- **代码生成**：根据描述创建新代码
- **代码重构**：改进现有代码结构和性能
- **Bug 修复**：分析和修复代码问题
- **测试编写**：创建单元测试和集成测试
- **文档生成**：为代码添加注释和文档
- **代码审查**：分析代码质量和最佳实践
- **项目分析**：理解和解释现有代码库结构
- **多语言支持**：支持各种编程语言（Pascal、Python、JavaScript 等）

#### AI 助手内置工具（通过 --verbose 可见）

**📁 文件操作工具**
- **`open_files`** - 打开和读取文件内容
- **`expand_code_chunks`** - 展开代码块查看完整内容
- **`create_file`** - 创建新文件（需要权限）
- **`edit_files`** - 修改文件内容（需要权限）

**⚡ 系统命令工具**
- **`powershell`** - 执行 PowerShell 命令（需要权限）
- **`run_command`** - 执行系统命令
- **`search_codebase`** - 在代码库中搜索特定模式

**🔗 Atlassian 集成工具**
- **Confluence 操作工具** - 创建、编辑、搜索页面
- **Jira 操作工具** - 管理问题、项目、工作流

**🔒 权限系统特性**
- 敏感操作需要用户明确授权
- 支持多级权限控制：一次、会话、总是、拒绝
- 透明的权限请求机制

### 配置管理
- `config` - 在编辑器中打开配置文件
- `log` - 在编辑器中打开日志文件  
- `mcp` - 在编辑器中打开 MCP 配置文件

## 使用示例

### 1. 首次使用需要登录：
```bash
# Rovo Dev AI 助手登录
.\acli.exe rovodev auth login

# Jira 功能登录
.\acli.exe jira auth login

# 管理员功能登录
.\acli.exe admin auth login

# 查看认证状态
.\acli.exe rovodev auth status
```

### 2. 运行 AI 编程助手：
```bash
# 基础启动（直接在当前代码上工作）
.\acli.exe rovodev run "帮我创建一个新的 React 组件"

# 不提供初始消息，启动后交互式输入
.\acli.exe rovodev run
```

### 3. 安全模式运行（强烈推荐）：
```bash
# 影子模式 - 在临时环境中工作
.\acli.exe rovodev run --shadow "重构这个函数"

# 详细模式 - 查看AI工作过程
.\acli.exe rovodev run --verbose --shadow "创建单元测试"

# 学习模式 - 了解AI如何工作
.\acli.exe rovodev run --verbose "分析这个文件的主要功能"

# 组合模式 - 最安全且透明的方式
.\acli.exe rovodev run --verbose --shadow "帮我优化这段代码"
```

### 4. 会话管理：
```bash
# 恢复上次会话
.\acli.exe rovodev run --restore

# 恢复会话并使用详细模式
.\acli.exe rovodev run --restore --verbose

# 复杂指令示例
.\acli.exe rovodev run --shadow "分析这个文件的性能问题并提供优化建议"

# 交互式启动（不提供初始消息）
.\acli.exe rovodev run --verbose --shadow
```

### 5. Jira 项目管理示例：
```bash
# 创建新项目
.\acli.exe jira project create --name "我的项目" --key "MP"

# 列出所有项目
.\acli.exe jira project list

# 创建工作项
.\acli.exe jira workitem create --project "MP" --summary "实现登录功能"

# 搜索工作项
.\acli.exe jira workitem search --project "MP" --status "待办"

# 分配任务
.\acli.exe jira workitem assign "MP-1" --assignee "<EMAIL>"
```

### 6. 详细模式 (--verbose) 深度使用：
```bash
# 学习AI如何分析代码
.\acli.exe rovodev run --verbose "请分析这个函数的复杂度"

# 观察AI如何处理多文件项目
.\acli.exe rovodev run --verbose "帮我重构这个模块"

# 了解AI如何执行命令行操作
.\acli.exe rovodev run --verbose "编译并测试这个项目"

# 查看AI如何与Atlassian工具集成
.\acli.exe rovodev run --verbose "在Confluence中创建这个功能的文档"
```

#### --verbose 模式显示的信息类型：
- **工具调用**：`└── Calling tool_name: {"parameters": "values"}`
- **文件操作**：显示打开、读取、修改的具体文件
- **命令执行**：显示执行的具体命令和参数
- **决策过程**：AI如何分解任务和选择工具
- **错误处理**：详细的错误信息和重试过程

#### --verbose 模式输出示例：
```
╭─ Response ─────────────────────────────────────────────────╮
│ 我来分析一下当前目录下的 testunit1.pas 文件。              │
╰────────────────────────────────────────────────────────────╯
  └── Calling open_files: {"file_paths": ["testunit1.pas"]}

  testunit1.pas:
    unit testunit1;
    {$mode objfpc}{$H+}
    {$codepage utf8}
    ...

╭─ Response ─────────────────────────────────────────────────╮
│ 我来展开查看文件的完整内容，以便更好地分析其主要功能。     │
╰────────────────────────────────────────────────────────────╯
  └── Calling expand_code_chunks: {"file_path": "testunit1.pas",
      "line_ranges": [[0,-1]]}
```

这种透明化的工作过程让用户能够：
- 🎓 **学习最佳实践** - 观察AI如何高效地处理代码任务
- 🔍 **调试问题** - 准确定位问题出现在哪个步骤
- 🚀 **优化工作流** - 了解如何更好地与AI协作

## 🔬 AI 助手深度分析（基于 --verbose 探索发现）

### 🛠️ 工具调用机制

#### 已确认的工具类型
通过 `--verbose` 模式实际观察到的工具调用：

**1. 文件操作工具**
```
└── Calling open_files: {"file_paths": ["testunit1.pas"]}
└── Calling expand_code_chunks: {"file_path": "testunit1.pas", "line_ranges": [[0,-1]]}
└── Calling create_file: {"file_path": "TestUtils.pas", "initial_content": "..."}
```

**2. 系统命令工具**
```
└── Calling powershell: {"command": "where lazbuild"}
```

#### 权限控制系统
AI 助手具有完善的权限控制机制：

**权限请求示例（实际界面）：**
```
╭─ Permissions required ───────────────────────────────────────────────────────────╮
│                                                                                  │
│ Requesting permission to use tool powershell with command 'lazbuild              │
│ testproject1.lpi'. Would you like to allow this tool?                            │
│                                                                                  │
│ > Allow (once)                                                                   │
│   Allow (session)                                                                │
│   Allow (always)                                                                 │
│   Deny (once)                                                                    │
│   Deny (session)                                                                 │
│   Deny (always)                                                                  │
│                                                                                  │
╰──────────────────────────────────────────────────────────────────────────────────╯
↑↓: Navigate | Enter ⏎: Select | Esc: Cancel
```

**权限选项详解：**
- **Allow (once)** - 仅本次操作允许，下次同样操作仍需确认
- **Allow (session)** - 本会话内允许，会话结束后重置
- **Allow (always)** - 永久允许此类操作，不再询问
- **Deny (once)** - 仅本次拒绝，下次同样操作仍会询问
- **Deny (session)** - 本会话内拒绝，会话结束后重置
- **Deny (always)** - 永久拒绝此类操作，不再询问

**操作方式：**
- 使用 `↑↓` 箭头键导航选项
- 按 `Enter` 确认选择
- 按 `Esc` 取消操作

**权限拒绝处理：**
```
Tool call to create_file was suppressed with reason:
User denied permission to use this function. DO NOT attempt...
```

### 🎯 AI 工作模式分析

#### 任务分解策略
AI 助手采用智能的任务分解方式：
1. **分析阶段** - 使用 `open_files` 了解项目结构
2. **深入分析** - 使用 `expand_code_chunks` 获取完整内容
3. **执行阶段** - 根据需要调用相应工具
4. **权限检查** - 敏感操作前请求用户授权

#### 错误恢复机制
- 权限被拒绝时，AI 会提供替代方案
- 工具调用失败时，会尝试其他方法
- 保持用户友好的交互体验

### 📊 性能特征

#### 响应模式
- **流式响应** - 实时显示处理进度（旋转图标）
- **分步执行** - 逐步显示工具调用过程
- **中断支持** - 支持 `CTRL+C` 中断操作

#### 处理能力
- **多文件处理** - 可同时打开多个文件
- **大文件支持** - 支持截断显示和展开查看
- **智能缓存** - 避免重复读取相同文件

### 🔐 安全机制深度解析

#### 权限分级系统
**低风险操作（无需权限）：**
- 读取文件内容
- 分析代码结构
- 提供建议和解释

**中等风险操作（需要权限）：**
- 执行系统命令
- 创建新文件
- 修改现有文件

**高风险操作（严格控制）：**
- 删除文件
- 系统配置修改
- 网络操作

#### 安全最佳实践
```bash
# 🛡️ 最安全的使用方式
.\acli.exe rovodev run --shadow --verbose "你的任务"

# 🔍 学习模式（只读操作）
.\acli.exe rovodev run --verbose "分析这个项目的架构"

# ⚠️ 谨慎使用（涉及文件修改）
.\acli.exe rovodev run --verbose "重构这个模块"
```

### � 获取和理解人工确认信息

#### 触发权限确认的方法
要获取 acli.exe 的人工确认信息，可以尝试以下操作：

```bash
# 1. 文件创建操作（会触发 create_file 权限请求）
.\acli.exe rovodev run --verbose "创建一个新的 Pascal 文件"

# 2. 系统命令执行（会触发 powershell 权限请求）
.\acli.exe rovodev run --verbose "编译这个项目"

# 3. 文件修改操作（会触发 edit_files 权限请求）
.\acli.exe rovodev run --verbose "重构这个函数"
```

#### 权限确认信息的结构
每个权限请求包含以下信息：

**1. 请求描述**
```
Requesting permission to use tool [工具名] with command '[具体命令]'
```

**2. 工具类型识别**
- `powershell` - PowerShell 命令执行
- `create_file` - 文件创建操作
- `edit_files` - 文件修改操作
- `confluence_*` - Confluence 操作
- `jira_*` - Jira 操作

**3. 命令详情**
显示具体要执行的命令或操作参数，帮助用户判断安全性

#### 权限决策指南

**🟢 建议允许的操作：**
- 读取文件内容
- 分析代码结构
- 生成文档或报告
- 编译和构建项目

**🟡 谨慎允许的操作：**
- 创建新文件
- 修改现有文件
- 执行系统命令
- 网络相关操作

**🔴 建议拒绝的操作：**
- 删除重要文件
- 修改系统配置
- 访问敏感数据
- 未知的网络请求

### �💡 高级使用技巧

#### 权限管理策略
- **首次使用**：选择 "Allow (once)" 测试功能
- **信任操作**：选择 "Allow (session)" 避免重复确认
- **危险操作**：选择 "Deny" 并使用 `--shadow` 模式
- **学习模式**：观察权限请求了解 AI 的工作方式

#### 工具调用优化
- 使用具体明确的指令减少不必要的工具调用
- 合理组织文件结构提高 AI 分析效率
- 定期清理临时文件和会话数据
- 通过权限设置控制 AI 的操作范围

#### 调试和故障排除
```bash
# 查看详细的工具调用过程
.\acli.exe rovodev run --verbose "你的任务"

# 在安全环境中测试
.\acli.exe rovodev run --shadow --verbose "测试任务"

# 恢复中断的会话
.\acli.exe rovodev run --restore --verbose

# 观察权限请求模式
.\acli.exe rovodev run --verbose "需要权限的操作"
```

### 7. 反馈和支持：
```bash
# 提交问题反馈
.\acli.exe feedback --summary "AI助手响应慢" --details "详细描述问题" --email "<EMAIL>"

# 生成PowerShell自动补全
.\acli.exe completion powershell
```

## 其他实用命令

### feedback - 反馈系统
提交请求或报告问题的专用命令，支持详细的问题描述和文件附件。

**使用格式：**
```bash
.\acli.exe feedback --summary "问题摘要" --details "详细描述" --email "<EMAIL>"
```

**主要选项：**
- `--summary` - 问题摘要
- `--details` - 详细描述
- `--email` - 接收反馈回复的邮箱地址
- `--attachments` - 附加多个文件
- `--time` - 问题发生的时间范围（如：1h, 15m）

### completion - 自动补全
为不同的 shell 生成自动补全脚本，提高命令行使用效率。

**支持的 shell：**
- `bash` - 生成 Bash 自动补全脚本
- `zsh` - 生成 Zsh 自动补全脚本
- `fish` - 生成 Fish 自动补全脚本
- `powershell` - 生成 PowerShell 自动补全脚本

### 版本信息
- `--version` - 查看 ACLI 版本信息
- `--help` - 显示帮助信息

## 重要说明

这个工具的主要价值在于 **Rovo Dev AI 编程助手**，它是一个智能编程伙伴，能理解自然语言指令并自动执行相应的编程任务。

### 使用建议

#### 🎯 不同场景的最佳实践

**🔰 新手用户（首次使用）**
```bash
.\acli.exe rovodev run --verbose --shadow "你的任务描述"
```
- 同时使用 `--verbose` 和 `--shadow` 确保安全且透明

**🛡️ 重要项目（生产环境）**
```bash
.\acli.exe rovodev run --shadow "任务描述"
```
- 始终使用 `--shadow` 模式确保安全

**📚 学习阶段（了解AI工作方式）**
```bash
.\acli.exe rovodev run --verbose "任务描述"
```
- 使用 `--verbose` 了解 AI 的工作过程和工具使用

**🔄 长期项目（保持连续性）**
```bash
.\acli.exe rovodev run --restore --verbose
```
- 利用 `--restore` 保持工作连续性

**🎯 最佳组合（推荐）**
```bash
.\acli.exe rovodev run --verbose --shadow --restore
```
- 安全 + 透明 + 连续性的完美组合

#### 💡 专业提示
- **影子模式**：在临时环境中测试更改，避免直接修改工作代码
- **详细模式**：学习AI如何分解任务、选择工具、处理问题
- **会话恢复**：保持长期项目的上下文和工作连续性
- **组合使用**：多个选项可以同时使用，获得最佳体验

## 系统信息

**当前版本：**
- ACLI 主程序：1.2.8-stable
- Rovo Dev CLI：0.8.0
- 支持系统：Windows 11 (AMD64)
- Git 集成：git version 2.50.0.windows.1

**认证状态示例：**
```
✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
```

## 故障排除和常见问题

### 🔧 常见问题解决

#### 认证问题
```bash
# 问题：认证失败或过期
# 解决：重新登录
.\acli.exe rovodev auth logout
.\acli.exe rovodev auth login

# 检查认证状态
.\acli.exe rovodev auth status
```

#### 性能问题
```bash
# 问题：AI 响应缓慢
# 解决：使用更具体的指令，避免处理大文件
.\acli.exe rovodev run --verbose "具体明确的任务描述"

# 问题：内存占用过高
# 解决：重启会话
# 使用 /exit 退出当前会话，然后重新启动
```

#### 文件操作问题
```bash
# 问题：文件修改失败
# 解决：检查文件权限，使用影子模式测试
.\acli.exe rovodev run --shadow --verbose "你的任务"

# 问题：编码问题（中文乱码）
# 解决：确保文件使用 UTF-8 编码
```

#### 网络连接问题
```bash
# 问题：连接 Atlassian 服务失败
# 解决：检查网络连接和防火墙设置
# 使用 --verbose 查看详细错误信息
```

### � 实际发现的工具清单

基于 `--verbose` 模式的实际测试，以下是已确认存在的工具：

#### ✅ 已确认工具
| 工具名称 | 功能描述 | 权限要求 | 使用示例 |
|---------|---------|---------|---------|
| `open_files` | 读取文件内容 | 无 | 分析代码结构 |
| `expand_code_chunks` | 展开代码块 | 无 | 查看完整文件 |
| `create_file` | 创建新文件 | 需要 | 生成新代码文件 |
| `powershell` | 执行 PowerShell 命令 | 需要 | 编译、构建项目 |

#### 🔍 推测存在的工具
基于功能描述和使用场景推测：
- `edit_files` - 修改现有文件
- `search_codebase` - 代码搜索
- `run_command` - 通用命令执行
- `confluence_*` - Confluence 操作系列
- `jira_*` - Jira 操作系列

#### 📊 工具调用统计
通过实际测试观察到的调用模式：
- **文件读取** - 最常用，几乎每个任务都会调用
- **代码展开** - 当文件内容被截断时自动调用
- **权限请求** - 涉及文件创建或命令执行时触发
- **错误处理** - 权限被拒绝时提供替代方案

### �📋 最佳实践检查清单

#### 使用前检查
- [ ] 已完成认证登录
- [ ] 在正确的项目目录中
- [ ] 网络连接正常
- [ ] 有足够的磁盘空间
- [ ] 了解权限控制机制

#### 安全使用检查
- [ ] 重要项目使用 `--shadow` 模式
- [ ] 定期备份重要代码
- [ ] 审查 AI 建议的更改
- [ ] 测试修改后的代码
- [ ] 谨慎授予文件修改权限

#### 学习优化检查
- [ ] 使用 `--verbose` 了解 AI 工作过程
- [ ] 编写清晰具体的指令
- [ ] 利用 `--restore` 保持工作连续性
- [ ] 定期查看日志文件
- [ ] 观察工具调用模式优化使用方式
- [ ] 学习权限确认机制用于自己的项目

### 🆘 获取帮助

#### 内置帮助
```bash
# 查看主帮助
.\acli.exe --help

# 查看特定模块帮助
.\acli.exe rovodev --help
.\acli.exe jira --help
.\acli.exe admin --help

# 查看子命令帮助
.\acli.exe rovodev run --help
```

#### 反馈渠道
```bash
# 提交问题反馈
.\acli.exe feedback --summary "问题摘要" --details "详细描述" --email "<EMAIL>"
```

#### 日志分析
```bash
# 打开日志文件查看详细信息
.\acli.exe rovodev log

# 使用 --verbose 模式获取实时调试信息
.\acli.exe rovodev run --verbose "你的任务"
```

## 高级功能和技巧

### 🎯 AI 助手交互技巧

#### 有效的指令编写
```bash
# ✅ 好的指令 - 具体明确
.\acli.exe rovodev run --verbose "分析 testunit1.pas 文件，找出性能瓶颈并提供优化建议"

# ✅ 好的指令 - 分步骤
.\acli.exe rovodev run --shadow "重构 SendToAI 方法，提高错误处理能力"

# ❌ 避免的指令 - 过于模糊
.\acli.exe rovodev run "帮我改代码"
```

#### 多轮对话技巧
- 使用 `--restore` 继续之前的对话
- 在对话中可以随时输入 `/` 查看可用命令
- 使用 `CTRL+C` 中断 AI 生成过程
- 使用 `/exit` 退出对话

#### 工作目录管理
- AI 助手会在当前工作目录中操作
- 确保在正确的项目目录中启动
- 使用 `--shadow` 模式避免意外修改

### 🔧 配置文件自定义

#### 默认配置位置
- 配置文件：`C:\Users\<USER>\.rovodev\config.yml`
- 日志文件：通过 `.\acli.exe rovodev log` 打开
- MCP 配置：通过 `.\acli.exe rovodev mcp` 打开

#### 自定义配置
```bash
# 使用自定义配置文件
.\acli.exe rovodev run --config-file "my_custom_config.yml" --verbose
```

### 🚀 性能优化建议

#### 提高响应速度
- 使用具体明确的指令
- 避免一次性处理过大的文件
- 合理使用 `--shadow` 模式（虽然安全但会增加处理时间）

#### 内存和资源管理
- 长时间使用后可以重启会话
- 定期清理临时文件
- 监控系统资源使用情况

## 基本工作流程

### Rovo Dev AI 助手工作流程
1. **认证登录**：`.\acli.exe rovodev auth login`
2. **检查状态**：`.\acli.exe rovodev auth status`
3. **启动助手**：`.\acli.exe rovodev run --verbose --shadow`
4. **自然语言交互**：用清晰具体的语言描述需求
5. **审查结果**：在影子模式下预览更改
6. **应用更改**：确认后应用到实际项目

### Jira 项目管理工作流程
1. **认证登录**：`.\acli.exe jira auth login`
2. **创建项目**：`.\acli.exe jira project create`
3. **管理工作项**：创建、分配、跟踪任务
4. **状态管理**：使用 transition 命令更新状态
5. **团队协作**：通过评论和分配功能协作
