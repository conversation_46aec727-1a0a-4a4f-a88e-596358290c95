version: 1

agent:
  # Additional system prompt to append to the agent's default system prompt
  additionalSystemPrompt: null
  # Enable streaming responses from the AI model
  streaming: false
  # Temperature setting for AI model responses (0.0-1.0)
  temperature: 0.1
  experimental:
    # Enable/disable the agent to run in shadow mode. This will run the agent on
    # a temporary clone of your workspace, prompting you before any changes are
    # applied to your working directory.
    enableShadowMode: false

sessions:
  # Automatically restore the last active session on startup
  autoRestore: false
  # Directory where session data is stored
  persistenceDir: C:\test\chatsessiontest\test_sessions

console:
  # Output format for console display (markdown, simple, or raw)
  outputFormat: simple
  # Show tool execution results in the console
  showToolResults: false

logging:
  # Path to the log file
  path: C:\test\chatsessiontest\aclilog\performance-optimized.log

mcp:
  # Path to the MCP (Model Context Protocol) configuration file
  mcpConfigPath: C:\Users\<USER>\.rovodev\mcp.json

toolPermissions:
  allowAll: false
  # Default permission for tools not explicitly listed
  default: deny
  # Permission settings for specific tools
  tools:
    open_files: allow
    expand_code_chunks: allow
    grep_file_content: allow
    find_and_replace_code: allow
    create_file: allow
    delete_file: ask
    getAccessibleAtlassianResources: deny
    getConfluenceSpaces: deny
    getConfluencePages: deny
    getPagesInConfluenceSpace: deny
    getConfluencePageAncestors: deny
    getConfluencePageFooterComments: deny
    getConfluencePageInlineComments: deny
    getConfluencePageDescendants: deny
    searchConfluenceUsingCql: deny
    getJiraIssue: deny
    getTransitionsForJiraIssue: deny
    lookupJiraAccountId: deny
    searchJiraIssuesUsingJql: deny
    getJiraIssueRemoteIssueLinks: deny
    getVisibleJiraProjects: deny
    getJiraProjectIssueTypesMetadata: deny
    createConfluencePage: deny
    updateConfluencePage: deny
    createConfluenceFooterComment: deny
    createConfluenceInlineComment: deny
    editJiraIssue: deny
    createJiraIssue: deny
    transitionJiraIssue: deny
    addCommentToJiraIssue: deny
    create_technical_plan: allow
  bash:
    # Default permission for bash commands not explicitly listed
    default: deny
    # List of specific bash commands with their permission settings
    commands:
    - command: echo.*
      permission: allow
    - command: git status
      permission: allow
    - command: git diff.*
      permission: allow
  # List of allowed MCP server names
  allowedMcpServers: []
