﻿{
    "created":  "2025-07-13T06:39:54.627Z",
    "metadata":  {
                     "testType":  "comprehensive",
                     "lastAccessed":  "2025-07-13T06:39:54.643Z",
                     "version":  "1.0"
                 },
    "id":  "comprehensive-test-20250713-063954",
    "messages":  [
                     {
                         "content":  "Hello, my name is <PERSON>, this is a test session",
                         "timestamp":  "2025-07-13T06:39:54.643Z",
                         "role":  "user"
                     },
                     {
                         "content":  "Hello <PERSON> San! I am CodeAssist Pro assistant, I will remember our conversation.",
                         "timestamp":  "2025-07-13T06:39:54.643Z",
                         "role":  "assistant"
                     }
                 ]
}
