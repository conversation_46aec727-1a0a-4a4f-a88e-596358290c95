# RovoDev GUI 项目最佳实践

## 📋 项目概述
这是一个基于 Lazarus/Free Pascal 的 GUI 应用程序，用于与 Atlassian RovoDev AI 进行交互。

## 🔧 编译最佳实践

### 推荐的编译工作流

1. **清理环境**：
   ```powershell
   # 停止运行的程序
   Stop-Process -Name "testproject1" -Force -ErrorAction SilentlyContinue
   
   # 清理编译缓存
   Remove-Item "lib\x86_64-win64\*.o", "lib\x86_64-win64\*.ppu" -Force -ErrorAction SilentlyContinue
   Remove-Item "testproject1.exe" -Force -ErrorAction SilentlyContinue
   ```

2. **使用 lazbuild 编译**（推荐）：
   ```powershell
   & "C:\lazarus\lazbuild.exe" testproject1.lpi
   ```

3. **使用 build.bat 脚本**（最稳定）：
   ```powershell
   Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat" -Wait -NoNewWindow
   ```

4. **备选：直接使用 FPC**：
   ```powershell
   & "C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.exe" -Mdelphi -Twin64 -O2 -vewnhi -Fu"C:\lazarus\components\lazutils" -Fu"C:\lazarus\lcl\units\x86_64-win64" -Fu"C:\lazarus\lcl" -Fu"C:\lazarus\packager\units\x86_64-win64" -Fu"C:\lazarus\components\lazcontrols\lib\x86_64-win64" testproject1.lpr
   ```

### 编译输出和错误诊断

#### ✅ **获取详细编译输出的正确方法**：

1. **使用 Start-Process 重定向**（最可靠）：
   ```powershell
   Start-Process -FilePath "C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.exe" -ArgumentList "-Mdelphi", "-Twin64", "-O2", "-vewnhi", "testproject1.lpr" -Wait -NoNewWindow -RedirectStandardOutput "compile_stdout.txt" -RedirectStandardError "compile_stderr.txt"
   ```

2. **通过 cmd.exe 执行 build.bat**：
   ```powershell
   Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat", ">", "build_output.txt", "2>&1" -Wait -NoNewWindow
   ```

3. **检查编译结果**：
   ```powershell
   if (Test-Path testproject1.exe) { 
     "✅ 编译成功！"; Get-ChildItem testproject1.exe | Select-Object Name, Length, LastWriteTime 
   } else { 
     "❌ 编译失败，查看错误："; Get-Content "compile_stderr.txt" 
   }
   ```

#### ❌ **不推荐的方法**（经常失败）：

```powershell
# 这些方法通常不能正确捕获输出
& "C:\lazarus\lazbuild.exe" testproject1.lpi > output.txt 2> error.txt
& "C:\lazarus\lazbuild.exe" testproject1.lpi 2>&1 | Tee-Object -FilePath "output.txt"
```

#### 🔍 **常见编译错误及解决方案**：

1. **"Can't find unit Interfaces"**：
   - 原因：LCL 路径问题或依赖变更
   - 解决：使用 build.bat 或检查 uses 子句

2. **"PPU Source not found"**：
   - 原因：需要重新编译依赖单元
   - 解决：清理所有编译缓存后重新编译

3. **"Checksum changed"**：
   - 原因：单元依赖关系改变
   - 解决：避免添加不必要的 uses 单元

### 编译参数说明

- `-Mdelphi`: Delphi 兼容模式
- `-Twin64`: Windows 64位目标
- `-O2`: O2 级别优化
- `-vewnhi`: 详细输出（错误、警告、注释、提示）
- `-Fu`: 指定单元搜索路径

### 故障排除

1. **编译失败时**：
   - 检查 Lazarus 安装路径
   - 清理所有编译缓存
   - 确保源文件没有语法错误

2. **修改未生效时**：
   - 强制清理 lib 目录
   - 停止所有相关进程
   - 重新编译

## 📁 文件结构最佳实践

### 核心文件
- `testproject1.lpr`: 主程序文件
- `testunit1.pas`: 主窗体单元
- `testunit1.lfm`: 窗体设计文件
- `SessionUtils.pas`: 会话处理工具单元

### 配置文件
- `performance_optimized_config.yml`: 性能优化配置
- `minimal_stable_config.yml`: 最小稳定配置
- `custom_persistence_config.yml`: 自定义持久化配置

### 工具脚本
- `build.bat`: 完整编译脚本
- `clean.bat`: 清理脚本

## 🔍 调试最佳实践

### 日志系统
程序使用 `gui_debug.log` 记录详细的运行日志：

```pascal
procedure WriteLog(const msg: string);
var
  logFile: TextFile;
  timeStr: string;
begin
  try
    timeStr := FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
    AssignFile(logFile, 'gui_debug.log');
    if FileExists('gui_debug.log') then
      Append(logFile)
    else
      Rewrite(logFile);
    WriteLn(logFile, Format('[%s] %s', [timeStr, msg]));
    CloseFile(logFile);
  except
    // 忽略日志错误
  end;
end;
```

### 关键调试点
1. **程序启动**: 检查配置文件加载
2. **会话初始化**: 验证会话目录路径
3. **acli.exe 查找**: 确认可执行文件位置
4. **认证状态**: 监控认证检查过程
5. **AI 交互**: 跟踪请求和响应

## ⚙️ 配置管理最佳实践

### 会话目录配置
程序会从配置文件中读取 `persistenceDir` 设置：

```yaml
sessions:
  persistenceDir: C:\test\chatsessiontest\test_sessions  # 绝对路径
  # 或
  persistenceDir: "test_sessions"  # 相对路径
```

### 路径处理
```pascal
// 智能路径处理 - 支持绝对路径和相对路径
if (Length(SessionsDir) > 1) and (SessionsDir[2] = ':') then
  // 绝对路径，直接使用
  SessionDir := IncludeTrailingPathDelimiter(SessionsDir) + LatestDir
else
  // 相对路径，加上当前目录
  SessionDir := IncludeTrailingPathDelimiter(GetCurrentDir) + 
               IncludeTrailingPathDelimiter(SessionsDir) + LatestDir;
```

### acli.exe 查找策略
程序会按优先级查找 acli.exe：

1. 当前目录: `acli.exe`
2. 上级目录: `..\acli.exe`
3. testacl目录: `testacl\acli.exe`

## 🚀 性能优化最佳实践

### 配置优化
- 使用 `performance_optimized_config.yml`
- 禁用不必要的功能（如流式响应、影子模式）
- 限制工具权限以减少网络调用

### 代码优化
- 使用异步进程执行避免界面冻结
- 实现超时机制防止无限等待
- 缓存认证状态减少重复检查

## 🛡️ 错误处理最佳实践

### 容错设计
```pascal
// 示例：容错的初始化
if not FileExists(ConfigFile) then
begin
  WriteLog('配置文件不存在，使用默认设置');
  // 继续执行而不是退出
end;
```

### 用户友好的错误信息
- 提供具体的错误描述
- 给出可能的解决方案
- 记录详细日志用于调试

## 📝 代码风格最佳实践

### 命名约定
- 使用有意义的变量名
- 函数名使用动词开头
- 常量使用大写字母

### 注释规范
```pascal
// 功能说明注释
WriteLog('=== 开始初始化会话 ===');

// 关键步骤注释
// 检查是否为绝对路径
if (Length(SessionsDir) > 1) and (SessionsDir[2] = ':') then
```

### 异常处理
```pascal
try
  // 主要逻辑
except
  on E: Exception do
  begin
    WriteLog('错误: ' + E.Message);
    // 适当的错误处理
  end;
end;
```

## 🧪 测试最佳实践

### 功能测试检查清单
- [ ] 程序启动和界面显示
- [ ] 配置文件正确读取
- [ ] acli.exe 成功找到
- [ ] 认证状态检查正常
- [ ] AI 对话功能工作
- [ ] 会话持久化正常
- [ ] 日志记录完整

### 测试环境
- 确保 Lazarus 正确安装
- 验证 acli.exe 可用
- 检查配置文件格式
- 测试不同的会话目录设置

## 📚 文档维护

### 更新此文档的时机
- 添加新功能时
- 修复重要 bug 时
- 改进编译流程时
- 发现新的最佳实践时

### 版本记录
- v1.0: 初始版本，包含基本编译和调试实践
- v1.1: 添加配置管理和路径处理最佳实践
- v1.2: 完善错误处理和性能优化指南
- v1.3: 添加编译输出捕获和错误诊断最佳实践，解决编译问题排查难题

---

*最后更新: 2025-07-15*
*维护者: RovoDev AI Assistant*