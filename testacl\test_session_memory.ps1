# 会话记忆功能测试脚本
# 创建时间: 2025-01-13

Write-Host "=== CodeAssistPro 会话记忆功能测试 ===" -ForegroundColor Green

# 1. 检查目录结构
Write-Host "`n1. 检查目录结构:" -ForegroundColor Yellow
Write-Host "当前目录: $(Get-Location)"
Write-Host "chat_sessions目录:" -NoNewline
if (Test-Path "chat_sessions") {
    Write-Host " ✓ 存在" -ForegroundColor Green
} else {
    Write-Host " ✗ 不存在" -ForegroundColor Red
}

Write-Host "logs目录:" -NoNewline
if (Test-Path "logs") {
    Write-Host " ✓ 存在" -ForegroundColor Green
} else {
    Write-Host " ✗ 不存在" -ForegroundColor Red
}

# 2. 检查配置文件
Write-Host "`n2. 检查配置文件:" -ForegroundColor Yellow
Write-Host "session_memory_config.yml:" -NoNewline
if (Test-Path "session_memory_config.yml") {
    Write-Host " ✓ 存在" -ForegroundColor Green
    # 检查会话配置
    $sessionConfig = Get-Content "session_memory_config.yml" | Select-String -A 10 "sessions:"
    Write-Host "会话配置预览:"
    $sessionConfig | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
} else {
    Write-Host " ✗ 不存在" -ForegroundColor Red
}

Write-Host "`nconfig.ini:" -NoNewline
if (Test-Path "config.ini") {
    Write-Host " ✓ 存在" -ForegroundColor Green
    # 检查ACLI配置路径
    $acliConfig = Get-Content "config.ini" | Select-String "config_file"
    Write-Host "ACLI配置路径:"
    Write-Host "  $acliConfig" -ForegroundColor Cyan
} else {
    Write-Host " ✗ 不存在" -ForegroundColor Red
}

# 3. 创建测试会话文件
Write-Host "`n3. 创建测试会话:" -ForegroundColor Yellow
$testSessionId = "test-session-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$sessionDir = "chat_sessions\$testSessionId"
New-Item -ItemType Directory -Path $sessionDir -Force | Out-Null

$testSessionData = @{
    id = $testSessionId
    created = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    messages = @(
        @{
            role = "user"
            content = "你好，我是测试用户"
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        },
        @{
            role = "assistant"
            content = "你好！我是CodeAssist Pro助手，我可以记住我们的对话历史。"
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
    )
    metadata = @{
        version = "1.0"
        lastAccessed = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    }
}

$testSessionData | ConvertTo-Json -Depth 10 | Out-File -FilePath "$sessionDir\session_context.json" -Encoding UTF8

Write-Host "测试会话已创建: $testSessionId" -ForegroundColor Green

# 4. 验证文件权限
Write-Host "`n4. 验证文件权限:" -ForegroundColor Yellow
try {
    $testFile = "chat_sessions\test_write.tmp"
    "test" | Out-File -FilePath $testFile -Encoding UTF8
    Remove-Item $testFile -Force
    Write-Host "文件写入权限: ✓ 正常" -ForegroundColor Green
} catch {
    Write-Host "文件写入权限: ✗ 异常 - $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 显示启动命令
Write-Host "`n5. 启动命令:" -ForegroundColor Yellow
Write-Host "请在此目录下运行以下命令启动服务:" -ForegroundColor Cyan
Write-Host "  ..\CodeAssistPro_Simple.exe" -ForegroundColor White
Write-Host "或者:" -ForegroundColor Cyan
Write-Host "  ..\acli.exe session_memory_config.yml" -ForegroundColor White

Write-Host "`n=== 测试完成 ===" -ForegroundColor Green
Write-Host "如果所有检查都通过，现在可以启动服务测试会话记忆功能了。" -ForegroundColor Yellow