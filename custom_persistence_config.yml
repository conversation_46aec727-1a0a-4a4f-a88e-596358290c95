version: 1

agent:
  # Additional system prompt to append to the agent's default system prompt
  additionalSystemPrompt: null
  # Enable streaming responses from the AI model
  streaming: false
  # Temperature setting for AI model responses (0.0-1.0)
  temperature: 0.3
  experimental:
    # Enable/disable the agent to run in shadow mode. This will run the agent on
    # a temporary clone of your workspace, prompting you before any changes are
    # applied to your working directory.
    enableShadowMode: false

sessions:
  # Automatically restore the last active session on startup
  autoRestore: true
  # Directory where session data is stored
  persistenceDir: C:\test\chatsessiontest\test_sessions

console:
  # Output format for console display (markdown, simple, or raw)
  outputFormat: simple
  # Show tool execution results in the console
  showToolResults: false

logging:
  # Path to the log file
  path: C:\test\chatsessiontest\test-acli.log

mcp:
  # Path to the MCP (Model Context Protocol) configuration file
  mcpConfigPath: C:\Users\<USER>\.rovodev\mcp.json

toolPermissions:
  allowAll: false
  # Default permission for tools not explicitly listed
  default: ask
  # Permission settings for specific tools
  tools:
    create_file: ask
    delete_file: allow
    find_and_replace_code: allow
    open_files: allow
    expand_code_chunks: allow
    expand_folder: allow
    grep_file_content: allow
    grep_file_paths: allow
    getAccessibleAtlassianResources: allow
    getConfluenceSpaces: allow
    getConfluencePages: allow
    getPagesInConfluenceSpace: allow
    getConfluencePageAncestors: allow
    getConfluencePageFooterComments: allow
    getConfluencePageInlineComments: allow
    getConfluencePageDescendants: allow
    searchConfluenceUsingCql: allow
    getJiraIssue: allow
    getTransitionsForJiraIssue: allow
    lookupJiraAccountId: allow
    searchJiraIssuesUsingJql: allow
    getJiraIssueRemoteIssueLinks: allow
    getVisibleJiraProjects: allow
    getJiraProjectIssueTypesMetadata: allow
    createConfluencePage: ask
    updateConfluencePage: ask
    createConfluenceFooterComment: ask
    createConfluenceInlineComment: ask
    editJiraIssue: ask
    createJiraIssue: ask
    transitionJiraIssue: ask
    addCommentToJiraIssue: ask
    create_technical_plan: allow
  bash:
    # Default permission for bash commands not explicitly listed
    default: ask
    # List of specific bash commands with their permission settings
    commands:
    - command: ls.*
      permission: allow
    - command: cat.*
      permission: allow
    - command: echo.*
      permission: allow
    - command: git status
      permission: allow
    - command: git diff.*
      permission: allow
    - command: git log.*
      permission: allow
    - command: pwd
      permission: allow
    - command: Get-Content test-acli.log | Select-Object -Last 5
      permission: allow
    - command: "& \"..\\acli.exe\" rovodev run --config-file \"C:\\test\\chatsessiontest\\\
        custom_persistence_config.yml\" \"\u5F53\u524D\u65F6\u95F4\u662F\u4EC0\u4E48\
        \uFF1F\" --no-stream"
      permission: allow
    - command: Get-Content test-acli.log | Select-Object -Last 10
      permission: allow
    - command: Stop-Process -Name "testproject1" -Force -ErrorAction SilentlyContinue
      permission: allow
    - command: Remove-Item "lib\x86_64-win64\testunit1.*" -Force -ErrorAction SilentlyContinue
      permission: allow
    - command: '& "C:\lazarus\lazbuild.exe" testproject1.lpi'
      permission: allow
    - command: "if (Test-Path testproject1.exe) { \"\u2705 \u7F16\u8BD1\u6210\u529F\
        \uFF01\"; Get-ChildItem testproject1.exe | Select-Object LastWriteTime } else\
        \ { \"\u274C \u7F16\u8BD1\u5931\u8D25\uFF01\" }"
      permission: allow
    - command: Start-Process -FilePath ".\testproject1.exe" -PassThru
      permission: allow
    - command: "Start-Sleep -Seconds 3; Get-Content gui_debug.log | Where-Object {\
        \ $_ -match \"\u4ECE\u914D\u7F6E\u6587\u4EF6\u8BFB\u53D6\u5230\u4F1A\u8BDD\
        \u76EE\u5F55|\u539F\u59CB\u884C\u5185\u5BB9\" } | Select-Object -Last 5"
      permission: allow
    - command: '& "C:\lazarus\lazbuild.exe" testproject1.lpi > compile_output.txt
        2> compile_error.txt'
      permission: allow
    - command: Remove-Item "testproject1.exe" -Force -ErrorAction SilentlyContinue
      permission: allow
    - command: Remove-Item "lib\x86_64-win64\*" -Force -ErrorAction SilentlyContinue
      permission: allow
    - command: '& "C:\lazarus\lazbuild.exe" testproject1.lpi *>&1 | Tee-Object -FilePath
        "compile_full_output.txt"'
      permission: allow
    - command: $output = & "C:\lazarus\lazbuild.exe" testproject1.lpi 2>&1; $output
        | Out-File "compile_full_output.txt"; $output
      permission: allow
    - command: Get-Content "fpc_compile_output.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-Content "fpc_stdout.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-Content "fpc_stderr.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-ChildItem testproject1.exe -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-ChildItem "fpc_*.txt" | ForEach-Object { Write-Host "=== $($_.Name)
        ==="; Get-Content $_.FullName }
      permission: allow
    - command: Get-ChildItem "C:\lazarus\lcl\units\x86_64-win64" | Where-Object {
        $_.Name -like "*interface*" } | Select-Object Name
      permission: allow
    - command: Get-ChildItem "C:\lazarus\lcl\interfaces" | Select-Object Name -First
        10
      permission: allow
    - command: Get-Content "compile_final_stdout.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: "if (Test-Path testproject1.exe) { \"\u2705 \u7F16\u8BD1\u6210\u529F\
        \uFF01\"; Get-ChildItem testproject1.exe | Select-Object Name, Length, LastWriteTime\
        \ } else { \"\u274C \u7F16\u8BD1\u5931\u8D25\uFF0C\u68C0\u67E5\u9519\u8BEF\
        \u4FE1\u606F\" }"
      permission: allow
    - command: Get-Content "compile_fixed_output.txt" | Select-Object -Last 20
      permission: allow
    - command: Remove-Item "testproject1.exe" -Force -ErrorAction SilentlyContinue;
        Remove-Item "lib\x86_64-win64\*" -Force -ErrorAction SilentlyContinue
      permission: allow
    - command: Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat",
        ">", "build_final.txt", "2>&1" -Wait -NoNewWindow; Get-Content "build_final.txt"
        | Select-Object -Last 15
      permission: allow
    - command: Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat",
        ">", "build_test.txt", "2>&1" -Wait -NoNewWindow; Get-Content "build_test.txt"
        | Select-Object -Last 10
      permission: allow
    - command: "if (Test-Path testproject1.exe) { \"\u2705 \u7F16\u8BD1\u6210\u529F\
        \uFF01\"; Get-ChildItem testproject1.exe | Select-Object LastWriteTime } else\
        \ { \"\u274C \u7F16\u8BD1\u5931\u8D25\" }"
      permission: allow
    - command: Remove-Item "lib\x86_64-win64\*.o", "lib\x86_64-win64\*.ppu" -Force
        -ErrorAction SilentlyContinue
      permission: allow
    - command: Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat",
        ">", "build_output.txt", "2>&1" -Wait -NoNewWindow
      permission: allow
    - command: Get-Content "build_output.txt" | Select-Object -Last 20
      permission: allow
    - command: "Get-Content \"build_output.txt\" | Where-Object { $_ -match \"Error|Fatal|\u9519\
        \u8BEF\" }"
      permission: allow
    - command: Copy-Item "backup\testunit1.pas" "testunit1.pas" -Force
      permission: allow
    - command: Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat",
        ">", "build_restore.txt", "2>&1" -Wait -NoNewWindow
      permission: allow
    - command: "if (Test-Path testproject1.exe) { \"\u2705 \u7F16\u8BD1\u6210\u529F\
        \uFF01\"; Get-ChildItem testproject1.exe | Select-Object Name, Length, LastWriteTime\
        \ } else { \"\u274C \u7F16\u8BD1\u4ECD\u7136\u5931\u8D25\" }"
      permission: allow
    - command: Copy-Item "backup\testproject1.lpi" "testproject1.lpi" -Force
      permission: allow
    - command: Copy-Item "backup\testproject1.lpr" "testproject1.lpr" -Force
      permission: allow
    - command: Copy-Item "backup\testunit1.lfm" "testunit1.lfm" -Force
      permission: allow
    - command: Start-Process -FilePath "cmd.exe" -ArgumentList "/c", "build.bat",
        ">", "build_backup_restore.txt", "2>&1" -Wait -NoNewWindow
      permission: allow
    - command: Get-Content "compile_stderr.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-Content "compile_stdout.txt" -ErrorAction SilentlyContinue
      permission: allow
    - command: Get-ChildItem testproject1.exe | Select-Object Name, Length, LastWriteTime
      permission: allow
    - command: "if (Test-Path testproject1.exe) { \"\u2705 \u7F16\u8BD1\u6210\u529F\
        \uFF01\"; Get-ChildItem testproject1.exe | Select-Object Name, Length, LastWriteTime\
        \ } else { \"\u274C \u7F16\u8BD1\u5931\u8D25\" }"
      permission: allow
    - command: Start-Sleep -Seconds 3; Get-Content gui_debug.log | Where-Object {
        $_ -match "$(Get-Date -Format 'yyyy-MM-dd HH:mm')" } | Select-Object -Last
        15
      permission: allow
    - command: Get-Content gui_debug.log | Select-Object -Last 20
      permission: allow
    - command: "Get-Content gui_debug.log | Where-Object { $_ -match \"SendToAI|Process\
        \ \u9000\u51FA\u4EE3\u7801|\u7528\u6237\u95EE\u9898\" } | Select-Object -Last\
        \ 10"
      permission: allow
  # List of allowed MCP server names
  allowedMcpServers: []
