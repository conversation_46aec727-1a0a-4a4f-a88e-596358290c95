Microsoft Windows [版本 10.0.22631.5624]
(c) Microsoft Corporation。保留所有权利。

C:\test\chatsessiontest>..\acli.exe rovodev run --config-file session_memory_config.yml "你好"

███████  ██████  ██    ██  ██████      ██████  ███████ ██    ██
██   ██ ██    ██ ██    ██ ██    ██     ██   ██ ██      ██    ██
██████  ██    ██ ██    ██ ██    ██     ██   ██ █████   ██    ██
██   ██ ██    ██  ██  ██  ██    ██     ██   ██ ██       ██  ██
██   ██  ██████    ████    ██████      ██████  ███████   ████

Welcome to Rovo Dev (beta), Atlassian's AI coding agent.

Here are some quick tips:

• Ask <PERSON><PERSON> Dev anything in your own words - from "explain this repo" to "add unit tests".
• Type "/" at any time to see available commands.
• Use CTRL+C to interrupt the agent during generation.
• Use /exit to quit.

Working in C:\test\chatsessiontest

WARNING: Unknown configuration fields found: ['maxHistoryLength', 'autoSaveInterval', 'sessionTimeout',
'enableSessionCompression']

╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ An unexpected error occurred, exiting.                                                                               │
╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
