@echo off
echo ========================================
echo Pascal/Lazarus 项目清理脚本
echo ========================================
echo.

REM 停止可能运行的程序
echo 正在停止运行中的程序...
taskkill /f /im testproject1.exe >nul 2>&1
timeout /t 1 /nobreak >nul

echo 正在清理编译产物...

REM 删除可执行文件和资源文件
if exist testproject1.exe (
    del /f /q testproject1.exe
    echo ✓ 删除 testproject1.exe
)

if exist testproject1.res (
    del /f /q testproject1.res
    echo ✓ 删除 testproject1.res
)

REM 删除编译缓存目录
if exist lib (
    rmdir /s /q lib
    echo ✓ 删除 lib 目录
)

echo 正在清理编译日志...

REM 删除编译输出文件
for %%f in (build_output.txt compile_output.txt compile_result.txt detailed_compile.txt) do (
    if exist %%f (
        del /f /q %%f
        echo ✓ 删除 %%f
    )
)

REM 删除错误日志文件
for %%f in (build_error.txt compile_error.txt latest_build_error.log) do (
    if exist %%f (
        del /f /q %%f
        echo ✓ 删除 %%f
    )
)

REM 删除临时文件
for %%f in (build_result.txt latest_build.log) do (
    if exist %%f (
        del /f /q %%f
        echo ✓ 删除 %%f
    )
)

REM 删除其他可能的编译产物
for %%f in (*.o *.ppu *.compiled) do (
    if exist %%f (
        del /f /q %%f
        echo ✓ 删除 %%f
    )
)

echo.
echo ========================================
echo 🧹 清理完成！
echo ========================================
echo.
echo 已清理的内容:
echo - 可执行文件 (.exe)
echo - 资源文件 (.res)
echo - 编译缓存 (lib 目录)
echo - 编译日志 (.txt, .log)
echo - 临时编译文件 (.o, .ppu, .compiled)
echo.
echo 保留的文件:
echo - 源代码文件 (.pas, .lpr, .lfm, .lpi)
echo - 配置文件 (.yml, .lps)
echo - 备份文件 (backup 目录)
echo.
