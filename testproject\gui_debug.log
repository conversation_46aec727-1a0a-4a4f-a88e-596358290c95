[2025-07-16 21:20:01] === GUI 程序启动 ===
[2025-07-16 21:20:01] 开始创建窗体
[2025-07-16 21:20:01] 设置窗体属性
[2025-07-16 21:20:01] 创建 GUI 控件
[2025-07-16 21:20:01] 开始创建 GUI 控件
[2025-07-16 21:20:01] 创建聊天显示区
[2025-07-16 21:20:01] GUI 控件创建完成
[2025-07-16 21:20:01] 创建初始化定时器
[2025-07-16 21:20:01] 初始化变量
[2025-07-16 21:20:01] 开始初始化会话
[2025-07-16 21:20:01] 开始初始化会话
[2025-07-16 21:20:01] 设置配置文件路径
[2025-07-16 21:20:01] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:20:01] 配置文件不存在
[2025-07-16 21:20:01] 会话初始化失败
[2025-07-16 21:20:01] 启动自动认证检查定时器
[2025-07-16 21:20:01] === GUI 程序启动完成 ===
[2025-07-16 21:20:01] === 窗体显示事件触发 ===
[2025-07-16 21:20:01] === 开始自动认证检查 ===
[2025-07-16 21:20:01] 调用 CheckAuthStatus
[2025-07-16 21:20:01] === 开始检查认证状态 ===
[2025-07-16 21:20:01] 设置 Process 参数
[2025-07-16 21:20:01] 设置 Process 选项
[2025-07-16 21:20:01] 开始执行认证状态检查
[2025-07-16 21:20:04] 检查认证状态时出错: Failed to execute  : 2
[2025-07-16 21:20:04] 认证状态检查结果: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:04] 用户邮箱: 
[2025-07-16 21:20:04] 认证状态: 检查失败
[2025-07-16 21:20:04] 是否已认证: False
[2025-07-16 21:20:04] === 更新窗体标题 ===
[2025-07-16 21:20:04] 窗体标题已更新: RovoDev 对话界面 - ❌ 检查失败
[2025-07-16 21:20:04] === 认证状态检查完成 ===
[2025-07-16 21:20:04] CheckAuthStatus 返回: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:04] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:20:04] === 开始自动认证检查 ===
[2025-07-16 21:20:04] 调用 CheckAuthStatus
[2025-07-16 21:20:04] === 开始检查认证状态 ===
[2025-07-16 21:20:04] 设置 Process 参数
[2025-07-16 21:20:04] 设置 Process 选项
[2025-07-16 21:20:04] 开始执行认证状态检查
[2025-07-16 21:20:06] 检查认证状态时出错: Failed to execute  : 2
[2025-07-16 21:20:06] 认证状态检查结果: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:06] 用户邮箱: 
[2025-07-16 21:20:06] 认证状态: 检查失败
[2025-07-16 21:20:06] 是否已认证: False
[2025-07-16 21:20:06] === 更新窗体标题 ===
[2025-07-16 21:20:06] 窗体标题已更新: RovoDev 对话界面 - ❌ 检查失败
[2025-07-16 21:20:06] === 认证状态检查完成 ===
[2025-07-16 21:20:06] CheckAuthStatus 返回: ❌ 检查失败: Failed to execute  : 2
[2025-07-16 21:20:06] 认证状态：未认证
[2025-07-16 21:20:06] === 自动认证检查完成 ===
[2025-07-16 21:20:06] 认证状态：未认证
[2025-07-16 21:20:06] === 自动认证检查完成 ===
[2025-07-16 21:25:45] === GUI 程序启动 ===
[2025-07-16 21:25:45] 开始创建窗体
[2025-07-16 21:25:45] 设置窗体属性
[2025-07-16 21:25:45] 创建 GUI 控件
[2025-07-16 21:25:45] 开始创建 GUI 控件
[2025-07-16 21:25:45] 创建聊天显示区
[2025-07-16 21:25:45] GUI 控件创建完成
[2025-07-16 21:25:45] 创建初始化定时器
[2025-07-16 21:25:45] 初始化变量
[2025-07-16 21:25:45] 开始初始化会话
[2025-07-16 21:25:45] 开始初始化会话
[2025-07-16 21:25:45] 设置配置文件路径
[2025-07-16 21:25:45] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:25:45] 检查 acli.exe
[2025-07-16 21:25:45] 会话初始化失败
[2025-07-16 21:25:45] 启动自动认证检查定时器
[2025-07-16 21:25:45] === GUI 程序启动完成 ===
[2025-07-16 21:25:45] === 窗体显示事件触发 ===
[2025-07-16 21:25:45] === 开始自动认证检查 ===
[2025-07-16 21:25:45] 调用 CheckAuthStatus
[2025-07-16 21:25:45] === 开始检查认证状态 ===
[2025-07-16 21:25:45] 设置 Process 参数
[2025-07-16 21:25:45] 设置 Process 选项
[2025-07-16 21:25:45] 开始执行认证状态检查
[2025-07-16 21:25:47] 认证状态检查完成，退出代码: 0
[2025-07-16 21:25:47] 获取到输出，行数: 1
[2025-07-16 21:25:47] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] 用户邮箱: 
[2025-07-16 21:25:47] 认证状态: 已认证
[2025-07-16 21:25:47] 是否已认证: True
[2025-07-16 21:25:47] === 更新窗体标题 ===
[2025-07-16 21:25:47] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:25:47] === 认证状态检查完成 ===
[2025-07-16 21:25:47] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:47] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:25:47] === 开始自动认证检查 ===
[2025-07-16 21:25:47] 调用 CheckAuthStatus
[2025-07-16 21:25:47] === 开始检查认证状态 ===
[2025-07-16 21:25:47] 设置 Process 参数
[2025-07-16 21:25:47] 设置 Process 选项
[2025-07-16 21:25:47] 开始执行认证状态检查
[2025-07-16 21:25:49] 认证状态检查完成，退出代码: 0
[2025-07-16 21:25:49] 获取到输出，行数: 1
[2025-07-16 21:25:49] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 用户邮箱: 
[2025-07-16 21:25:49] 认证状态: 已认证
[2025-07-16 21:25:49] 是否已认证: True
[2025-07-16 21:25:49] === 更新窗体标题 ===
[2025-07-16 21:25:49] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:25:49] === 认证状态检查完成 ===
[2025-07-16 21:25:49] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:25:49] 认证状态：已认证
[2025-07-16 21:25:49] === 自动认证检查完成 ===
[2025-07-16 21:25:49] 认证状态：已认证
[2025-07-16 21:25:49] === 自动认证检查完成 ===
[2025-07-16 21:26:06] === 开始状态检查 ===
[2025-07-16 21:26:06] === 开始检查认证状态 ===
[2025-07-16 21:26:06] 设置 Process 参数
[2025-07-16 21:26:06] 设置 Process 选项
[2025-07-16 21:26:06] 开始执行认证状态检查
[2025-07-16 21:26:08] 认证状态检查完成，退出代码: 0
[2025-07-16 21:26:08] 获取到输出，行数: 1
[2025-07-16 21:26:08] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:26:08] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:26:08] 用户邮箱: 
[2025-07-16 21:26:08] 认证状态: 已认证
[2025-07-16 21:26:08] 是否已认证: True
[2025-07-16 21:26:08] === 更新窗体标题 ===
[2025-07-16 21:26:08] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:26:08] === 认证状态检查完成 ===
[2025-07-16 21:26:08] === 状态检查完成 ===
[2025-07-16 21:26:10] === 开始发送问题 ===
[2025-07-16 21:26:10] 用户问题: 测试
[2025-07-16 21:26:10] 禁用按钮和输入框
[2025-07-16 21:26:10] 聊天计数: 1
[2025-07-16 21:26:10] 开始调用 SendToAI
[2025-07-16 21:26:10] 进入 SendToAI 方法，问题: 测试
[2025-07-16 21:26:10] 显示发送状态
[2025-07-16 21:26:10] 创建 TProcess
[2025-07-16 21:26:10] 设置 Process 参数
[2025-07-16 21:26:10] 设置 Process 选项
[2025-07-16 21:26:10] 开始执行 Process (异步模式)
[2025-07-16 21:26:10] Process 启动成功，等待完成...
[2025-07-16 21:26:16] 等待进程完成... 5秒
[2025-07-16 21:26:22] 等待进程完成... 10秒
[2025-07-16 21:26:27] 等待进程完成... 15秒
[2025-07-16 21:26:33] 等待进程完成... 20秒
[2025-07-16 21:26:38] 等待进程完成... 25秒
[2025-07-16 21:26:44] 等待进程完成... 30秒
[2025-07-16 21:26:49] 等待进程完成... 35秒
[2025-07-16 21:26:55] 等待进程完成... 40秒
[2025-07-16 21:27:00] 等待进程完成... 45秒
[2025-07-16 21:27:06] 等待进程完成... 50秒
[2025-07-16 21:27:11] 等待进程完成... 55秒
[2025-07-16 21:27:17] 等待进程完成... 60秒
[2025-07-16 21:27:17] 进程超时，强制终止
[2025-07-16 21:27:17] SendToAI 返回，回复长度: 0
[2025-07-16 21:30:10] === GUI 程序启动 ===
[2025-07-16 21:30:10] 开始创建窗体
[2025-07-16 21:30:10] 设置窗体属性
[2025-07-16 21:30:10] 创建 GUI 控件
[2025-07-16 21:30:10] 开始创建 GUI 控件
[2025-07-16 21:30:10] 创建聊天显示区
[2025-07-16 21:30:10] GUI 控件创建完成
[2025-07-16 21:30:10] 创建初始化定时器
[2025-07-16 21:30:10] 初始化变量
[2025-07-16 21:30:10] 开始初始化会话
[2025-07-16 21:30:10] 开始初始化会话
[2025-07-16 21:30:10] 设置配置文件路径
[2025-07-16 21:30:10] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:30:10] 检查 acli.exe
[2025-07-16 21:30:10] 会话初始化失败
[2025-07-16 21:30:10] 启动自动认证检查定时器
[2025-07-16 21:30:10] === GUI 程序启动完成 ===
[2025-07-16 21:30:10] === 窗体显示事件触发 ===
[2025-07-16 21:30:10] === 开始自动认证检查 ===
[2025-07-16 21:30:10] 调用 CheckAuthStatus
[2025-07-16 21:30:10] === 开始检查认证状态 ===
[2025-07-16 21:30:10] 设置 Process 参数
[2025-07-16 21:30:10] 设置 Process 选项
[2025-07-16 21:30:10] 开始执行认证状态检查
[2025-07-16 21:30:12] 认证状态检查完成，退出代码: 0
[2025-07-16 21:30:12] 获取到输出，行数: 1
[2025-07-16 21:30:12] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] 用户邮箱: 
[2025-07-16 21:30:12] 认证状态: 已认证
[2025-07-16 21:30:12] 是否已认证: True
[2025-07-16 21:30:12] === 更新窗体标题 ===
[2025-07-16 21:30:12] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:30:12] === 认证状态检查完成 ===
[2025-07-16 21:30:12] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:12] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:30:12] === 开始自动认证检查 ===
[2025-07-16 21:30:12] 调用 CheckAuthStatus
[2025-07-16 21:30:12] === 开始检查认证状态 ===
[2025-07-16 21:30:12] 设置 Process 参数
[2025-07-16 21:30:12] 设置 Process 选项
[2025-07-16 21:30:12] 开始执行认证状态检查
[2025-07-16 21:30:14] 认证状态检查完成，退出代码: 0
[2025-07-16 21:30:14] 获取到输出，行数: 1
[2025-07-16 21:30:14] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 用户邮箱: 
[2025-07-16 21:30:14] 认证状态: 已认证
[2025-07-16 21:30:14] 是否已认证: True
[2025-07-16 21:30:14] === 更新窗体标题 ===
[2025-07-16 21:30:14] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:30:14] === 认证状态检查完成 ===
[2025-07-16 21:30:14] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:30:14] 认证状态：已认证
[2025-07-16 21:30:14] === 自动认证检查完成 ===
[2025-07-16 21:30:14] 认证状态：已认证
[2025-07-16 21:30:14] === 自动认证检查完成 ===
[2025-07-16 21:31:58] === GUI 程序启动 ===
[2025-07-16 21:31:58] 开始创建窗体
[2025-07-16 21:31:58] 设置窗体属性
[2025-07-16 21:31:58] 创建 GUI 控件
[2025-07-16 21:31:58] 开始创建 GUI 控件
[2025-07-16 21:31:58] 创建聊天显示区
[2025-07-16 21:31:58] GUI 控件创建完成
[2025-07-16 21:31:58] 创建初始化定时器
[2025-07-16 21:31:58] 初始化变量
[2025-07-16 21:31:58] 开始初始化会话
[2025-07-16 21:31:58] 开始初始化会话
[2025-07-16 21:31:58] 设置配置文件路径
[2025-07-16 21:31:58] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:31:58] 检查 acli.exe
[2025-07-16 21:31:58] 会话初始化失败
[2025-07-16 21:31:58] 启动自动认证检查定时器
[2025-07-16 21:31:58] === GUI 程序启动完成 ===
[2025-07-16 21:31:58] === 窗体显示事件触发 ===
[2025-07-16 21:31:58] === 开始自动认证检查 ===
[2025-07-16 21:31:58] 调用 CheckAuthStatus
[2025-07-16 21:31:58] === 开始检查认证状态 ===
[2025-07-16 21:31:58] 设置 Process 参数
[2025-07-16 21:31:58] 设置 Process 选项
[2025-07-16 21:31:58] 开始执行认证状态检查
[2025-07-16 21:32:00] 认证状态检查完成，退出代码: 0
[2025-07-16 21:32:00] 获取到输出，行数: 1
[2025-07-16 21:32:00] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] 用户邮箱: 
[2025-07-16 21:32:00] 认证状态: 已认证
[2025-07-16 21:32:00] 是否已认证: True
[2025-07-16 21:32:00] === 更新窗体标题 ===
[2025-07-16 21:32:00] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:32:00] === 认证状态检查完成 ===
[2025-07-16 21:32:00] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:00] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:32:00] === 开始自动认证检查 ===
[2025-07-16 21:32:00] 调用 CheckAuthStatus
[2025-07-16 21:32:00] === 开始检查认证状态 ===
[2025-07-16 21:32:00] 设置 Process 参数
[2025-07-16 21:32:00] 设置 Process 选项
[2025-07-16 21:32:00] 开始执行认证状态检查
[2025-07-16 21:32:02] 认证状态检查完成，退出代码: 0
[2025-07-16 21:32:02] 获取到输出，行数: 1
[2025-07-16 21:32:02] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 用户邮箱: 
[2025-07-16 21:32:02] 认证状态: 已认证
[2025-07-16 21:32:02] 是否已认证: True
[2025-07-16 21:32:02] === 更新窗体标题 ===
[2025-07-16 21:32:02] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:32:02] === 认证状态检查完成 ===
[2025-07-16 21:32:02] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:32:02] 认证状态：已认证
[2025-07-16 21:32:02] === 自动认证检查完成 ===
[2025-07-16 21:32:02] 认证状态：已认证
[2025-07-16 21:32:02] === 自动认证检查完成 ===
[2025-07-16 21:32:14] === 开始发送问题 ===
[2025-07-16 21:32:14] 用户问题: 你好
[2025-07-16 21:32:14] 禁用按钮和输入框
[2025-07-16 21:32:14] 聊天计数: 1
[2025-07-16 21:32:14] 开始调用 SendToAI
[2025-07-16 21:32:14] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:32:14] 显示发送状态
[2025-07-16 21:32:14] 创建 TProcess
[2025-07-16 21:32:14] 设置 Process 参数
[2025-07-16 21:32:14] 设置 Process 选项
[2025-07-16 21:32:14] 开始执行 Process (异步模式)
[2025-07-16 21:32:14] Process 启动成功，等待完成...
[2025-07-16 21:32:19] 等待进程完成... 5秒
[2025-07-16 21:32:25] 等待进程完成... 10秒
[2025-07-16 21:32:30] 等待进程完成... 15秒
[2025-07-16 21:32:36] 等待进程完成... 20秒
[2025-07-16 21:32:42] 等待进程完成... 25秒
[2025-07-16 21:32:47] 等待进程完成... 30秒
[2025-07-16 21:32:51] Process 执行完成
[2025-07-16 21:32:51] Process 退出代码: 0
[2025-07-16 21:32:57] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:32:57] 会话目录: 
[2025-07-16 21:32:57] 步骤1：检查会话文件: \session_context.json
[2025-07-16 21:32:57] ❌ 会话文件不存在
[2025-07-16 21:32:57] SendToAI 返回，回复长度: 0
[2025-07-16 21:37:36] === GUI 程序启动 ===
[2025-07-16 21:37:36] 开始创建窗体
[2025-07-16 21:37:36] 设置窗体属性
[2025-07-16 21:37:36] 创建 GUI 控件
[2025-07-16 21:37:36] 开始创建 GUI 控件
[2025-07-16 21:37:36] 创建聊天显示区
[2025-07-16 21:37:36] GUI 控件创建完成
[2025-07-16 21:37:36] 创建初始化定时器
[2025-07-16 21:37:36] 初始化变量
[2025-07-16 21:37:36] 开始初始化会话
[2025-07-16 21:37:36] 开始初始化会话
[2025-07-16 21:37:36] 设置配置文件路径
[2025-07-16 21:37:36] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:37:36] 检查 acli.exe
[2025-07-16 21:37:36] 会话初始化成功
[2025-07-16 21:37:36] 界面初始化完成
[2025-07-16 21:37:36] 启动自动认证检查定时器
[2025-07-16 21:37:36] === GUI 程序启动完成 ===
[2025-07-16 21:37:36] === 窗体显示事件触发 ===
[2025-07-16 21:37:36] === 开始自动认证检查 ===
[2025-07-16 21:37:36] 调用 CheckAuthStatus
[2025-07-16 21:37:36] === 开始检查认证状态 ===
[2025-07-16 21:37:36] 设置 Process 参数
[2025-07-16 21:37:36] 设置 Process 选项
[2025-07-16 21:37:36] 开始执行认证状态检查
[2025-07-16 21:37:36] 
[2025-07-16 21:37:38] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:38] 获取到输出，行数: 1
[2025-07-16 21:37:38] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] 用户邮箱: 
[2025-07-16 21:37:38] 认证状态: 已认证
[2025-07-16 21:37:38] 是否已认证: True
[2025-07-16 21:37:38] === 更新窗体标题 ===
[2025-07-16 21:37:38] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:38] === 认证状态检查完成 ===
[2025-07-16 21:37:38] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:38] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:37:38] === 开始自动认证检查 ===
[2025-07-16 21:37:38] 调用 CheckAuthStatus
[2025-07-16 21:37:38] === 开始检查认证状态 ===
[2025-07-16 21:37:38] 设置 Process 参数
[2025-07-16 21:37:38] 设置 Process 选项
[2025-07-16 21:37:38] 开始执行认证状态检查
[2025-07-16 21:37:38] 
[2025-07-16 21:37:40] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:40] 获取到输出，行数: 1
[2025-07-16 21:37:40] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 用户邮箱: 
[2025-07-16 21:37:40] 认证状态: 已认证
[2025-07-16 21:37:40] 是否已认证: True
[2025-07-16 21:37:40] === 更新窗体标题 ===
[2025-07-16 21:37:40] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:40] === 认证状态检查完成 ===
[2025-07-16 21:37:40] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:40] 认证状态：已认证
[2025-07-16 21:37:40] === 自动认证检查完成 ===
[2025-07-16 21:37:40] 认证状态：已认证
[2025-07-16 21:37:40] === 自动认证检查完成 ===
[2025-07-16 21:37:45] === 开始状态检查 ===
[2025-07-16 21:37:45] === 开始检查认证状态 ===
[2025-07-16 21:37:45] 设置 Process 参数
[2025-07-16 21:37:45] 设置 Process 选项
[2025-07-16 21:37:45] 开始执行认证状态检查
[2025-07-16 21:37:45] 
[2025-07-16 21:37:47] 认证状态检查完成，退出代码: 0
[2025-07-16 21:37:47] 获取到输出，行数: 1
[2025-07-16 21:37:47] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:47] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:37:47] 用户邮箱: 
[2025-07-16 21:37:47] 认证状态: 已认证
[2025-07-16 21:37:47] 是否已认证: True
[2025-07-16 21:37:47] === 更新窗体标题 ===
[2025-07-16 21:37:47] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:37:47] === 认证状态检查完成 ===
[2025-07-16 21:37:47] === 状态检查完成 ===
[2025-07-16 21:37:54] === 开始发送问题 ===
[2025-07-16 21:37:54] 用户问题: 你好
[2025-07-16 21:37:54] 禁用按钮和输入框
[2025-07-16 21:37:54] 聊天计数: 1
[2025-07-16 21:37:54] 开始调用 SendToAI
[2025-07-16 21:37:54] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:37:54] 显示发送状态
[2025-07-16 21:37:54] 创建 TProcess
[2025-07-16 21:37:54] 设置 Process 参数
[2025-07-16 21:37:54] 设置 Process 选项
[2025-07-16 21:37:54] 开始执行 Process (异步模式)
[2025-07-16 21:37:54] Process 启动成功，等待完成...
[2025-07-16 21:37:59] 等待进程完成... 5秒
[2025-07-16 21:38:05] 等待进程完成... 10秒
[2025-07-16 21:38:10] 等待进程完成... 15秒
[2025-07-16 21:38:16] 等待进程完成... 20秒
[2025-07-16 21:38:22] 等待进程完成... 25秒
[2025-07-16 21:38:27] 等待进程完成... 30秒
[2025-07-16 21:38:29] Process 执行完成
[2025-07-16 21:38:29] Process 退出代码: 0
[2025-07-16 21:38:34] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:38:34] 会话目录: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:38:34] 步骤1：检查会话文件: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-16 21:38:34] ✅ 会话文件存在
[2025-07-16 21:38:34] 步骤2：测试 GetSessionInfo
[2025-07-16 21:38:34] ✅ GetSessionInfo 成功
[2025-07-16 21:38:34] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:38:34] 消息数量: 4
[2025-07-16 21:38:34] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-16 21:40:14] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-16 21:40:14] SendToAI 返回，回复长度: 0
[2025-07-16 21:45:06] === GUI 程序启动 ===
[2025-07-16 21:45:06] 开始创建窗体
[2025-07-16 21:45:06] 设置窗体属性
[2025-07-16 21:45:06] 创建 GUI 控件
[2025-07-16 21:45:06] 开始创建 GUI 控件
[2025-07-16 21:45:06] 创建聊天显示区
[2025-07-16 21:45:06] GUI 控件创建完成
[2025-07-16 21:45:06] 创建初始化定时器
[2025-07-16 21:45:06] 初始化变量
[2025-07-16 21:45:06] 开始初始化会话
[2025-07-16 21:45:06] 开始初始化会话
[2025-07-16 21:45:06] 设置配置文件路径
[2025-07-16 21:45:06] 检查配置文件: custom_persistence_config.yml
[2025-07-16 21:45:06] 检查 acli.exe
[2025-07-16 21:45:06] 会话初始化成功
[2025-07-16 21:45:06] 界面初始化完成
[2025-07-16 21:45:06] 启动自动认证检查定时器
[2025-07-16 21:45:06] === GUI 程序启动完成 ===
[2025-07-16 21:45:06] === 窗体显示事件触发 ===
[2025-07-16 21:45:06] === 开始自动认证检查 ===
[2025-07-16 21:45:06] 调用 CheckAuthStatus
[2025-07-16 21:45:06] === 开始检查认证状态 ===
[2025-07-16 21:45:06] 设置 Process 参数
[2025-07-16 21:45:06] 设置 Process 选项
[2025-07-16 21:45:06] 开始执行认证状态检查
[2025-07-16 21:45:06] 命令：
[2025-07-16 21:45:08] 认证状态检查完成，退出代码: 0
[2025-07-16 21:45:08] 获取到输出，行数: 1
[2025-07-16 21:45:08] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] 用户邮箱: 
[2025-07-16 21:45:08] 认证状态: 已认证
[2025-07-16 21:45:08] 是否已认证: True
[2025-07-16 21:45:08] === 更新窗体标题 ===
[2025-07-16 21:45:08] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:45:08] === 认证状态检查完成 ===
[2025-07-16 21:45:08] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:08] === 定时器触发，开始自动认证检查 ===
[2025-07-16 21:45:08] === 开始自动认证检查 ===
[2025-07-16 21:45:08] 调用 CheckAuthStatus
[2025-07-16 21:45:08] === 开始检查认证状态 ===
[2025-07-16 21:45:08] 设置 Process 参数
[2025-07-16 21:45:08] 设置 Process 选项
[2025-07-16 21:45:08] 开始执行认证状态检查
[2025-07-16 21:45:08] 命令：
[2025-07-16 21:45:10] 认证状态检查完成，退出代码: 0
[2025-07-16 21:45:10] 获取到输出，行数: 1
[2025-07-16 21:45:10] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 用户邮箱: 
[2025-07-16 21:45:10] 认证状态: 已认证
[2025-07-16 21:45:10] 是否已认证: True
[2025-07-16 21:45:10] === 更新窗体标题 ===
[2025-07-16 21:45:10] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 21:45:10] === 认证状态检查完成 ===
[2025-07-16 21:45:10] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 21:45:10] 认证状态：已认证
[2025-07-16 21:45:10] === 自动认证检查完成 ===
[2025-07-16 21:45:10] 认证状态：已认证
[2025-07-16 21:45:10] === 自动认证检查完成 ===
[2025-07-16 21:45:23] === 开始发送问题 ===
[2025-07-16 21:45:23] 用户问题: 你好
[2025-07-16 21:45:23] 禁用按钮和输入框
[2025-07-16 21:45:23] 聊天计数: 1
[2025-07-16 21:45:23] 开始调用 SendToAI
[2025-07-16 21:45:23] 进入 SendToAI 方法，问题: 你好
[2025-07-16 21:45:23] 显示发送状态
[2025-07-16 21:45:23] 创建 TProcess
[2025-07-16 21:45:23] 设置 Process 参数
[2025-07-16 21:45:23] 设置 Process 选项
[2025-07-16 21:45:23] 开始执行 Process (异步模式)
[2025-07-16 21:45:23] 命令：
[2025-07-16 21:45:23] Process 启动成功，等待完成...
[2025-07-16 21:45:28] 等待进程完成... 5秒
[2025-07-16 21:45:34] 等待进程完成... 10秒
[2025-07-16 21:45:39] 等待进程完成... 15秒
[2025-07-16 21:45:45] 等待进程完成... 20秒
[2025-07-16 21:45:50] 等待进程完成... 25秒
[2025-07-16 21:45:56] 等待进程完成... 30秒
[2025-07-16 21:45:59] Process 执行完成
[2025-07-16 21:45:59] Process 退出代码: 0
[2025-07-16 21:46:04] 开始逐步测试 SessionUtils 函数
[2025-07-16 21:46:04] 会话目录: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:46:04] 步骤1：检查会话文件: C:\test\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-16 21:46:04] ✅ 会话文件存在
[2025-07-16 21:46:04] 步骤2：测试 GetSessionInfo
[2025-07-16 21:46:04] ✅ GetSessionInfo 成功
[2025-07-16 21:46:04] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-16 21:46:04] 消息数量: 6
[2025-07-16 21:46:04] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-16 21:48:01] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-16 21:48:01] SendToAI 返回，回复长度: 0
[2025-07-17 06:01:18] === GUI 程序启动 ===
[2025-07-17 06:01:18] 开始创建窗体
[2025-07-17 06:01:18] 设置窗体属性
[2025-07-17 06:01:18] 创建 GUI 控件
[2025-07-17 06:01:18] 开始创建 GUI 控件
[2025-07-17 06:01:18] 创建聊天显示区
[2025-07-17 06:01:18] GUI 控件创建完成
[2025-07-17 06:01:18] 创建初始化定时器
[2025-07-17 06:01:18] 初始化变量
[2025-07-17 06:01:18] 开始初始化会话
[2025-07-17 06:01:18] 开始初始化会话
[2025-07-17 06:01:18] 设置配置文件路径
[2025-07-17 06:01:18] 检查配置文件: custom_persistence_config.yml
[2025-07-17 06:01:18] 检查 acli.exe
[2025-07-17 06:01:18] 会话初始化成功
[2025-07-17 06:01:18] 界面初始化完成
[2025-07-17 06:01:18] 启动自动认证检查定时器
[2025-07-17 06:01:18] === GUI 程序启动完成 ===
[2025-07-17 06:01:19] === 窗体显示事件触发 ===
[2025-07-17 06:01:19] === 开始自动认证检查 ===
[2025-07-17 06:01:19] 调用 CheckAuthStatus
[2025-07-17 06:01:19] === 开始检查认证状态 ===
[2025-07-17 06:01:19] 设置 Process 参数
[2025-07-17 06:01:19] 设置 Process 选项
[2025-07-17 06:01:19] 开始执行认证状态检查
[2025-07-17 06:01:19] 命令：
[2025-07-17 06:01:22] 认证状态检查完成，退出代码: 0
[2025-07-17 06:01:22] 获取到输出，行数: 1
[2025-07-17 06:01:22] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] 用户邮箱: 
[2025-07-17 06:01:22] 认证状态: 已认证
[2025-07-17 06:01:22] 是否已认证: True
[2025-07-17 06:01:22] === 更新窗体标题 ===
[2025-07-17 06:01:22] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:01:22] === 认证状态检查完成 ===
[2025-07-17 06:01:22] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:22] === 定时器触发，开始自动认证检查 ===
[2025-07-17 06:01:22] === 开始自动认证检查 ===
[2025-07-17 06:01:22] 调用 CheckAuthStatus
[2025-07-17 06:01:22] === 开始检查认证状态 ===
[2025-07-17 06:01:22] 设置 Process 参数
[2025-07-17 06:01:22] 设置 Process 选项
[2025-07-17 06:01:22] 开始执行认证状态检查
[2025-07-17 06:01:22] 命令：
[2025-07-17 06:01:25] 认证状态检查完成，退出代码: 0
[2025-07-17 06:01:25] 获取到输出，行数: 1
[2025-07-17 06:01:25] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 用户邮箱: 
[2025-07-17 06:01:25] 认证状态: 已认证
[2025-07-17 06:01:25] 是否已认证: True
[2025-07-17 06:01:25] === 更新窗体标题 ===
[2025-07-17 06:01:25] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:01:25] === 认证状态检查完成 ===
[2025-07-17 06:01:25] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:01:25] 认证状态：已认证
[2025-07-17 06:01:25] === 自动认证检查完成 ===
[2025-07-17 06:01:25] 认证状态：已认证
[2025-07-17 06:01:25] === 自动认证检查完成 ===
[2025-07-17 06:01:33] === 开始发送问题 ===
[2025-07-17 06:01:33] 用户问题: 你好
[2025-07-17 06:01:33] 禁用按钮和输入框
[2025-07-17 06:01:33] 聊天计数: 1
[2025-07-17 06:01:33] 开始调用 SendToAI
[2025-07-17 06:01:33] 进入 SendToAI 方法，问题: 你好
[2025-07-17 06:01:33] 显示发送状态
[2025-07-17 06:01:33] 创建 TProcess
[2025-07-17 06:01:33] 设置 Process 参数
[2025-07-17 06:01:33] 设置 Process 选项
[2025-07-17 06:01:33] 开始执行 Process (异步模式)
[2025-07-17 06:01:33] 命令：
[2025-07-17 06:01:33] Process 启动成功，等待完成...
[2025-07-17 06:01:38] 等待进程完成... 5秒
[2025-07-17 06:01:44] 等待进程完成... 10秒
[2025-07-17 06:01:49] 等待进程完成... 15秒
[2025-07-17 06:01:55] 等待进程完成... 20秒
[2025-07-17 06:02:00] 等待进程完成... 25秒
[2025-07-17 06:02:06] 等待进程完成... 30秒
[2025-07-17 06:02:11] 等待进程完成... 35秒
[2025-07-17 06:02:17] 等待进程完成... 40秒
[2025-07-17 06:02:20] Process 执行完成
[2025-07-17 06:02:20] Process 退出代码: 0
[2025-07-17 06:02:25] 开始逐步测试 SessionUtils 函数
[2025-07-17 06:02:25] 会话目录: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 06:02:25] 步骤1：检查会话文件: C:\test\chatsessiontest\testproject\custom_sessions\76576117-e7bd-4d04-a8be-377e6da573c2\session_context.json
[2025-07-17 06:02:25] ✅ 会话文件存在
[2025-07-17 06:02:26] 步骤2：测试 GetSessionInfo
[2025-07-17 06:02:26] ✅ GetSessionInfo 成功
[2025-07-17 06:02:26] 会话ID: 76576117-e7bd-4d04-a8be-377e6da573c2
[2025-07-17 06:02:26] 消息数量: 8
[2025-07-17 06:02:26] 步骤3：测试 ExtractLatestResponseFromJSON
[2025-07-17 06:02:26] ❌ ExtractLatestResponseFromJSON 异常: File not open
[2025-07-17 06:02:26] SendToAI 返回，回复长度: 0
[2025-07-17 06:08:05] === GUI 程序启动 ===
[2025-07-17 06:08:05] 开始创建窗体
[2025-07-17 06:08:05] 设置窗体属性
[2025-07-17 06:08:05] 创建 GUI 控件
[2025-07-17 06:08:05] 开始创建 GUI 控件
[2025-07-17 06:08:05] 创建聊天显示区
[2025-07-17 06:08:05] GUI 控件创建完成
[2025-07-17 06:08:05] 创建初始化定时器
[2025-07-17 06:08:05] 初始化变量
[2025-07-17 06:08:05] 开始初始化会话
[2025-07-17 06:08:05] 开始初始化会话
[2025-07-17 06:08:05] 设置配置文件路径
[2025-07-17 06:08:05] 检查配置文件: custom_persistence_config.yml
[2025-07-17 06:08:05] 检查 acli.exe
[2025-07-17 06:08:05] 会话初始化成功
[2025-07-17 06:08:05] 界面初始化完成
[2025-07-17 06:08:05] 启动自动认证检查定时器
[2025-07-17 06:08:05] === GUI 程序启动完成 ===
[2025-07-17 06:08:05] === 窗体显示事件触发 ===
[2025-07-17 06:08:05] === 开始自动认证检查 ===
[2025-07-17 06:08:05] 调用 CheckAuthStatus
[2025-07-17 06:08:05] === 开始检查认证状态 ===
[2025-07-17 06:08:05] 设置 Process 参数
[2025-07-17 06:08:05] 设置 Process 选项
[2025-07-17 06:08:05] 开始执行认证状态检查
[2025-07-17 06:08:05] 命令：
[2025-07-17 06:08:07] 认证状态检查完成，退出代码: 0
[2025-07-17 06:08:07] 获取到输出，行数: 1
[2025-07-17 06:08:07] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] 用户邮箱: 
[2025-07-17 06:08:07] 认证状态: 已认证
[2025-07-17 06:08:07] 是否已认证: True
[2025-07-17 06:08:07] === 更新窗体标题 ===
[2025-07-17 06:08:07] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:08:07] === 认证状态检查完成 ===
[2025-07-17 06:08:07] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:07] === 定时器触发，开始自动认证检查 ===
[2025-07-17 06:08:07] === 开始自动认证检查 ===
[2025-07-17 06:08:07] 调用 CheckAuthStatus
[2025-07-17 06:08:07] === 开始检查认证状态 ===
[2025-07-17 06:08:07] 设置 Process 参数
[2025-07-17 06:08:07] 设置 Process 选项
[2025-07-17 06:08:07] 开始执行认证状态检查
[2025-07-17 06:08:07] 命令：
[2025-07-17 06:08:09] 认证状态检查完成，退出代码: 0
[2025-07-17 06:08:09] 获取到输出，行数: 1
[2025-07-17 06:08:09] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 用户邮箱: 
[2025-07-17 06:08:09] 认证状态: 已认证
[2025-07-17 06:08:09] 是否已认证: True
[2025-07-17 06:08:09] === 更新窗体标题 ===
[2025-07-17 06:08:09] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-17 06:08:09] === 认证状态检查完成 ===
[2025-07-17 06:08:09] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-17 06:08:09] 认证状态：已认证
[2025-07-17 06:08:09] === 自动认证检查完成 ===
[2025-07-17 06:08:09] 认证状态：已认证
[2025-07-17 06:08:09] === 自动认证检查完成 ===
[2025-07-17 06:08:32] === 开始发送问题 ===
[2025-07-17 06:08:32] 用户问题: 测试
[2025-07-17 06:08:32] 禁用按钮和输入框
[2025-07-17 06:08:32] 聊天计数: 1
[2025-07-17 06:08:32] 开始调用 SendToAI
[2025-07-17 06:08:32] 进入 SendToAI 方法，问题: 测试
[2025-07-17 06:08:32] 显示发送状态
[2025-07-17 06:08:32] 创建 TProcess
[2025-07-17 06:08:32] 设置 Process 参数
[2025-07-17 06:08:32] 设置 Process 选项
[2025-07-17 06:08:32] 开始执行 Process (异步模式)
[2025-07-17 06:08:32] 命令：
[2025-07-17 06:08:32] Process 启动成功，等待完成...
[2025-07-17 06:08:38] 等待进程完成... 5秒
[2025-07-17 06:08:43] 等待进程完成... 10秒
[2025-07-17 06:08:49] 等待进程完成... 15秒
[2025-07-17 06:08:54] 等待进程完成... 20秒
[2025-07-17 06:09:00] 等待进程完成... 25秒
[2025-07-17 06:09:05] 等待进程完成... 30秒
[2025-07-17 06:09:11] 等待进程完成... 35秒
[2025-07-17 06:09:16] 等待进程完成... 40秒
[2025-07-17 06:09:22] 等待进程完成... 45秒
[2025-07-17 06:09:27] 等待进程完成... 50秒
[2025-07-17 06:09:32] 等待进程完成... 55秒
[2025-07-17 06:09:38] 等待进程完成... 60秒
[2025-07-17 06:09:38] 进程超时，强制终止
[2025-07-17 06:09:38] SendToAI 返回，回复长度: 0
