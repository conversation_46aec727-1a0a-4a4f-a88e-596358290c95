@echo off
echo ===== Rovodev Config Test Script =====
echo.

cd /d "%~dp0"
echo Current directory: %CD%
echo.

echo Testing rovodev with different configurations...
echo.

echo ========================================
echo Test 1: Default rovodev (no config)
echo ========================================
echo Command: ..\acli.exe rovodev run "Hello, this is a test"
echo.
..\acli.exe rovodev run "Hello, this is a test"
echo.
echo Exit code: %ERRORLEVEL%
echo.

echo ========================================
echo Test 2: Minimal config file
echo ========================================
echo Command: ..\acli.exe rovodev run --config-file working_config.yml
echo Starting interactive mode with config - type your message and /exit to quit
echo.
pause
..\acli.exe rovodev run --config-file working_config.yml
echo.
echo Exit code: %ERRORLEVEL%
echo.

echo ========================================
echo Test 3: Original config file
echo ========================================
echo Command: ..\acli.exe rovodev run --config-file session_memory_config.yml "Hello original config"
echo.
..\acli.exe rovodev run --config-file session_memory_config.yml "Hello original config"
echo.
echo Exit code: %ERRORLEVEL%
echo.

echo ========================================
echo Test 4: Interactive mode with config
echo ========================================
echo Command: ..\acli.exe rovodev run --config-file working_config.yml
echo This will start interactive mode - type your messages and /exit to quit
echo.
pause
..\acli.exe rovodev run --config-file working_config.yml

echo.
echo ========================================
echo All tests completed!
echo Check the chat_sessions directory for new session files
echo.
pause