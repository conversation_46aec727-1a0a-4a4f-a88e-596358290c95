﻿# 会话记忆功能测试脚本
# 创建时间: 2025-01-13

Write-Host "=== CodeAssistPro 会话记忆功能测试 ===" -ForegroundColor Green

# 1. 妫€鏌ョ洰褰曠粨鏋?
Write-Host "`n1. 妫€鏌ョ洰褰曠粨鏋?" -ForegroundColor Yellow
Write-Host "褰撳墠鐩綍: $(Get-Location)"
Write-Host "chat_sessions鐩綍:" -NoNewline
if (Test-Path "chat_sessions") {
    Write-Host " 鉁?瀛樺湪" -ForegroundColor Green
} else {
    Write-Host " 鉁?涓嶅瓨鍦? -ForegroundColor Red
}

Write-Host "logs鐩綍:" -NoNewline
if (Test-Path "logs") {
    Write-Host " 鉁?瀛樺湪" -ForegroundColor Green
} else {
    Write-Host " 鉁?涓嶅瓨鍦? -ForegroundColor Red
}

# 2. 妫€鏌ラ厤缃枃浠?
Write-Host "`n2. 妫€鏌ラ厤缃枃浠?" -ForegroundColor Yellow
Write-Host "session_memory_config.yml:" -NoNewline
if (Test-Path "session_memory_config.yml") {
    Write-Host " 鉁?瀛樺湪" -ForegroundColor Green
    # 妫€鏌ヤ細璇濋厤缃?
    $sessionConfig = Get-Content "session_memory_config.yml" | Select-String -A 10 "sessions:"
    Write-Host "浼氳瘽閰嶇疆棰勮:"
    $sessionConfig | ForEach-Object { Write-Host "  $_" -ForegroundColor Cyan }
} else {
    Write-Host " 鉁?涓嶅瓨鍦? -ForegroundColor Red
}

Write-Host "`nconfig.ini:" -NoNewline
if (Test-Path "config.ini") {
    Write-Host " 鉁?瀛樺湪" -ForegroundColor Green
    # 妫€鏌CLI閰嶇疆璺緞
    $acliConfig = Get-Content "config.ini" | Select-String "config_file"
    Write-Host "ACLI閰嶇疆璺緞:"
    Write-Host "  $acliConfig" -ForegroundColor Cyan
} else {
    Write-Host " 鉁?涓嶅瓨鍦? -ForegroundColor Red
}

# 3. 鍒涘缓娴嬭瘯浼氳瘽鏂囦欢
Write-Host "`n3. 鍒涘缓娴嬭瘯浼氳瘽:" -ForegroundColor Yellow
$testSessionId = "test-session-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
$sessionDir = "chat_sessions\$testSessionId"
New-Item -ItemType Directory -Path $sessionDir -Force | Out-Null

$testSessionData = @{
    id = $testSessionId
    created = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    messages = @(
        @{
            role = "user"
            content = "浣犲ソ锛屾垜鏄祴璇曠敤鎴?
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        },
        @{
            role = "assistant"
            content = "浣犲ソ锛佹垜鏄疌odeAssist Pro鍔╂墜锛屾垜鍙互璁颁綇鎴戜滑鐨勫璇濆巻鍙层€?
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        }
    )
    metadata = @{
        version = "1.0"
        lastAccessed = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
    }
}

$testSessionData | ConvertTo-Json -Depth 10 | Out-File -FilePath "$sessionDir\session_context.json" -Encoding UTF8

Write-Host "娴嬭瘯浼氳瘽宸插垱寤? $testSessionId" -ForegroundColor Green

# 4. 楠岃瘉鏂囦欢鏉冮檺
Write-Host "`n4. 楠岃瘉鏂囦欢鏉冮檺:" -ForegroundColor Yellow
try {
    $testFile = "chat_sessions\test_write.tmp"
    "test" | Out-File -FilePath $testFile -Encoding UTF8
    Remove-Item $testFile -Force
    Write-Host "鏂囦欢鍐欏叆鏉冮檺: 鉁?姝ｅ父" -ForegroundColor Green
} catch {
    Write-Host "鏂囦欢鍐欏叆鏉冮檺: 鉁?寮傚父 - $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 鏄剧ず鍚姩鍛戒护
Write-Host "`n5. 鍚姩鍛戒护:" -ForegroundColor Yellow
Write-Host "璇峰湪姝ょ洰褰曚笅杩愯浠ヤ笅鍛戒护鍚姩鏈嶅姟:" -ForegroundColor Cyan
Write-Host "  ..\CodeAssistPro_Simple.exe" -ForegroundColor White
Write-Host "鎴栬€?" -ForegroundColor Cyan
Write-Host "  C:\prg2\acli.exe --config session_memory_config.yml" -ForegroundColor White

Write-Host "`n=== 娴嬭瘯瀹屾垚 ===" -ForegroundColor Green
Write-Host "濡傛灉鎵€鏈夋鏌ラ兘閫氳繃锛岀幇鍦ㄥ彲浠ュ惎鍔ㄦ湇鍔℃祴璇曚細璇濊蹇嗗姛鑳戒簡銆? -ForegroundColor Yellow
