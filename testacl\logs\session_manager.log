[02:27:02] SessionManager Starting - 2025-07-13 02:27:02
[02:27:02] Received SessionDir param: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions"
[02:27:02] Param length: 62
[02:27:02] Param is empty: False
[02:27:02] Set FSessionDir: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions"
[02:27:02] Initialization complete, monitoring dir: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions"
[02:27:02] Directory exists check: True
[02:27:02] Current working dir: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chatsessiontest"
[02:27:02] Starting session directory monitoring
[02:27:02] Scan #1 starting
[02:27:02] 开始扫描目录: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions"
[02:27:02] 目录存在检查: True
[02:27:02] 开始查找子目录...
[02:27:02] 找到 1 个子目录
[02:27:02] 检查目录[0]: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions\0522517e-1310-4cf1-adce-f8d9c0c56a44"
[02:27:02] 检查文件: "C:\prg2\rovodev\sessiontest\CodeAssistPro_Simple\chat_sessions\0522517e-1310-4cf1-adce-f8d9c0c56a44\session_context.json"
[02:27:02] 文件存在: True
[02:27:02] 文件修改时间: 2025/7/10 11:53:20
[02:27:02] ✅ 发现新Session: 0522517e-1310-4cf1-adce-f8d9c0c56a44
[02:27:02] 扫描完成，当前映射数量: 1
[02:27:02] Scan #1 completed, waiting 5 seconds...
[02:27:07] Monitoring thread ended
[02:27:07] SessionManager shutting down
