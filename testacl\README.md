# CodeAssistPro 会话记忆功能测试环境

## 目录说明

这是一个专门用于测试CodeAssistPro会话记忆功能的干净环境。

### 文件结构
```
chatsessiontest/
├── session_memory_config.yml  # ACLI配置文件（启用会话记忆）
├── config.ini                 # 应用程序配置文件
├── chat_sessions/             # 会话数据存储目录
├── logs/                      # 日志文件目录
├── test_session_memory.ps1    # 测试脚本
└── README.md                  # 本说明文件
```

## 配置特点

### 会话记忆优化配置
- **persistenceDir**: `./chat_sessions` - 会话数据存储在当前目录
- **maxHistoryLength**: `100` - 支持100条历史消息
- **autoSaveInterval**: `10` - 每10秒自动保存
- **sessionTimeout**: `3600` - 1小时会话超时
- **enableSessionCompression**: `false` - 禁用压缩便于调试

### 日志配置
- **level**: `debug` - 详细调试日志
- **path**: `./logs/session_memory.log` - 专用日志文件
- **includeStackTrace**: `true` - 包含堆栈跟踪

## 使用方法

### 重要说明
- **语言要求**: 所有对话和回复必须使用中文
- **进度记录**: 由于程序可能自动退出，每个测试步骤的进度都需要记录到进度文件中

### 1. 运行测试脚本
```powershell
.\test_session_memory.ps1
```

### 2. 启动服务
在chatsessiontest目录下运行：
```powershell
..\CodeAssistPro_Simple.exe
```

### 3. 测试会话记忆
1. 发送第一条消息：`你好，我叫张三`
2. 发送第二条消息：`我刚才说我叫什么名字？`
3. 检查助手是否能记住你的名字
4. **重要**: 每个步骤完成后，将结果记录到 `progress.txt` 文件中

### 4. 检查会话文件
会话数据会保存在 `chat_sessions/` 目录下，每个会话一个子目录。

## 进度记录

### 进度文件格式
创建 `progress.txt` 文件记录每个测试步骤：
```
[时间戳] 步骤描述 - 状态 (成功/失败/进行中)
[时间戳] 详细结果或错误信息
---
```

### 进度记录示例
```
[2024-01-13 10:30:00] 启动测试脚本 - 成功
[2024-01-13 10:30:15] 启动CodeAssistPro服务 - 成功
[2024-01-13 10:31:00] 发送第一条消息"你好，我叫张三" - 成功
[2024-01-13 10:31:30] 发送第二条消息询问姓名 - 成功
[2024-01-13 10:32:00] 验证会话记忆功能 - 成功，助手记住了姓名
---
```

## 故障排除

### 检查进度文件
```powershell
Get-Content progress.txt -Tail 10
```

### 检查日志
```powershell
Get-Content logs\session_memory.log -Tail 20
```

### 检查会话文件
```powershell
Get-ChildItem chat_sessions -Recurse
```

### 验证配置
```powershell
Get-Content session_memory_config.yml | Select-String -A 10 "sessions:"
```

## 关键配置说明

1. **相对路径**: 使用 `./chat_sessions` 确保会话数据存储在当前目录
2. **调试模式**: 启用详细日志和堆栈跟踪
3. **频繁保存**: 10秒自动保存确保数据不丢失
4. **禁用压缩**: 便于直接查看会话文件内容
5. **长超时**: 1小时超时避免会话过早结束

## 预期行为

- **中文交互**: 所有对话必须使用中文进行
- **会话记忆**: 会话数据应该保存在 `chat_sessions/` 目录
- **数据持久化**: 每次对话都会更新会话文件
- **会话恢复**: 重启服务后应该能恢复之前的对话历史
- **详细日志**: 日志文件应该记录详细的会话操作信息
- **进度跟踪**: 每个测试步骤都应记录在 `progress.txt` 文件中，防止因程序退出而丢失进度

## 注意事项

1. **自动退出问题**: 如果程序自动退出，请检查 `progress.txt` 文件了解最后执行的步骤
2. **中文回复**: 确保所有测试对话都使用中文，验证中文会话记忆功能
3. **进度备份**: 建议定期备份 `progress.txt` 和 `chat_sessions/` 目录
4. **错误记录**: 遇到错误时，详细记录错误信息到进度文件中

## 重要发现

### 测试目标明确
**关键理解**: 
- **当前AI实例**: 这个正在对话的Rovo Dev实例是**没有使用配置文件启动的**
- **测试目标**: 需要启动一个**新的AI实例**，并且这个新实例要使用`session_memory_config.yml`配置文件
- **测试目的**: 验证新实例是否能正确加载和使用配置文件中的会话记忆设置

### 正确的测试流程
1. **启动新实例**: 使用配置文件启动新的acli实例
   ```batch
   ..\acli.exe .\session_memory_config.yml
   ```
2. **测试会话记忆**: 在新实例中测试会话记忆功能
3. **验证配置生效**: 确认新实例使用了配置文件中的设置
4. **检查数据持久化**: 验证会话数据正确保存到指定目录

### 实例区别
- **当前实例**: 无配置文件，用于开发和调试
- **测试实例**: 使用session_memory_config.yml，用于验证会话记忆功能