@echo off
echo ========================================
echo testproject1 GUI 应用编译脚本
echo ========================================
echo.

REM 设置编译器路径
set FPC_PATH=C:\lazarus\fpc\3.2.2\bin\x86_64-win64\fpc.exe
set LAZARUS_PATH=C:\lazarus
set LCL_PATH=%LAZARUS_PATH%\lcl\units\x86_64-win64
set LAZUTILS_PATH=%LAZARUS_PATH%\components\lazutils

REM 检查编译器是否存在
if not exist "%FPC_PATH%" (
    echo 错误: Free Pascal 编译器未找到
    echo 路径: %FPC_PATH%
    echo 请检查 Lazarus 安装路径
    pause
    exit /b 1
)

REM 检查 Lazarus 组件路径
if not exist "%LAZUTILS_PATH%" (
    echo 错误: Lazarus 组件路径未找到
    echo 路径: %LAZUTILS_PATH%
    echo 请检查 Lazarus 安装路径
    pause
    exit /b 1
)

echo 编译器路径: %FPC_PATH%
echo Lazarus 路径: %LAZARUS_PATH%
echo LCL 路径: %LCL_PATH%
echo LazUtils 路径: %LAZUTILS_PATH%
echo.

REM 编译 testproject1 (GUI应用程序)
echo 编译 testproject1.lpr...
echo.
echo 使用参数:
echo - 模式: Delphi 兼容模式 (-Mdelphi)
echo - 目标: Windows 64位 (-Twin64)
echo - 优化: O2 级别优化 (-O2)
echo - 详细输出: 显示错误、警告、注释、提示 (-vewnhi)
echo.

"%FPC_PATH%" -Mdelphi -Twin64 -O2 -vewnhi -Fu"%LAZUTILS_PATH%" -Fu"%LCL_PATH%" -Fu"%LAZARUS_PATH%\lcl" -Fu"%LAZARUS_PATH%\packager\units\x86_64-win64" -Fu"%LAZARUS_PATH%\components\lazcontrols\lib\x86_64-win64" testproject1.lpr

if %ERRORLEVEL% equ 0 (
    echo.
    echo ✓ testproject1.exe 编译成功！
    echo.
    if exist testproject1.exe (
        echo 文件信息:
        dir testproject1.exe | find "testproject1.exe"
        echo.
        echo 最后修改时间:
        forfiles /m testproject1.exe /c "cmd /c echo @fdate @ftime" 2>nul
    )
) else (
    echo.
    echo ✗ testproject1.lpr 编译失败 (错误代码: %ERRORLEVEL%)
    echo.
    echo 可能的原因:
    echo 1. 语法错误 - 请检查 Pascal 代码
    echo 2. 缺少依赖单元 - 请检查 uses 子句
    echo 3. LCL 路径问题 - 请检查 Lazarus 安装
    echo.
    echo 建议:
    echo 1. 使用 Lazarus IDE 打开 testproject1.lpi 查看详细错误
    echo 2. 检查 SessionUtils.pas 是否有语法错误
    echo 3. 确保所有必需的 LCL 组件已安装
    echo.
    exit /b 1
)
echo.


echo ========================================
echo 🎉 testproject1 编译完成！
echo ========================================
echo.
echo 生成的可执行文件:
echo - testproject1.exe           (RovoDev GUI 对话界面)
echo.
echo 使用方法:
echo 1. 确保 acli.exe 在上级目录 (..\acli.exe)
echo 2. 确保配置文件 performance_optimized_config.yml 存在
echo 3. 双击运行 testproject1.exe
echo 4. 使用图形界面与 RovoDev AI 进行对话
echo.
echo 功能特性:
echo - 图形化用户界面
echo - 实时认证状态检查
echo - 会话持久化
echo - Unicode 中文支持
echo - 详细的日志记录
echo.
if exist testproject1.exe (
    echo ✅ 编译成功！可以运行 testproject1.exe
) else (
    echo ❌ 编译失败！请检查上面的错误信息
)
echo.
