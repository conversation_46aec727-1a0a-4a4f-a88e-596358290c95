﻿# CodeAssistPro Session Memory Comprehensive Test Script
# With automatic progress logging
# Created: 2025-07-13

param(
    [string]$ProgressFile = "progress.txt"
)

# Progress logging function
function Write-Progress {
    param(
        [string]$Message,
        [string]$Status = "In Progress",
        [string]$Details = ""
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "[$timestamp] $Message - $Status"
    if ($Details) {
        $logEntry += "`r`n[$timestamp] $Details"
    }
    $logEntry += "`r`n"
    
    # Display to console
    Write-Host "[$timestamp] $Message - $Status" -ForegroundColor $(
        switch ($Status) {
            "Success" { "Green" }
            "Failed" { "Red" }
            "In Progress" { "Yellow" }
            default { "White" }
        }
    )
    if ($Details) {
        Write-Host "[$timestamp] $Details" -ForegroundColor Cyan
    }
    
    # Write to progress file
    try {
        Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    } catch {
        Write-Warning "Failed to write to progress file: $($_.Exception.Message)"
    }
}

# Start test
Write-Host "=== CodeAssistPro Session Memory Comprehensive Test ===" -ForegroundColor Green
Write-Progress "Starting comprehensive test" "In Progress"

# Phase 1: Environment Check
Write-Host "`n=== Phase 1: Environment Check ===" -ForegroundColor Yellow
Write-Progress "Environment check phase" "Started"

$currentDir = Get-Location
Write-Progress "Current directory check" "Success" "Location: $currentDir"

# Check chat_sessions directory
if (Test-Path "chat_sessions") {
    Write-Progress "chat_sessions directory check" "Success" "Directory exists"
} else {
    Write-Progress "chat_sessions directory check" "Failed" "Directory missing, creating..."
    try {
        New-Item -ItemType Directory -Path "chat_sessions" -Force | Out-Null
        Write-Progress "chat_sessions directory creation" "Success"
    } catch {
        Write-Progress "chat_sessions directory creation" "Failed" $_.Exception.Message
    }
}

# Check logs directory
if (Test-Path "logs") {
    Write-Progress "logs directory check" "Success" "Directory exists"
} else {
    Write-Progress "logs directory check" "Failed" "Directory missing, creating..."
    try {
        New-Item -ItemType Directory -Path "logs" -Force | Out-Null
        Write-Progress "logs directory creation" "Success"
    } catch {
        Write-Progress "logs directory creation" "Failed" $_.Exception.Message
    }
}

# Phase 2: Configuration Check
Write-Host "`n=== Phase 2: Configuration Check ===" -ForegroundColor Yellow
Write-Progress "Configuration check phase" "Started"

if (Test-Path "session_memory_config.yml") {
    Write-Progress "session_memory_config.yml check" "Success"
    try {
        $sessionConfig = Get-Content "session_memory_config.yml" | Select-String -A 10 "sessions:"
        Write-Host "Session configuration preview:" -ForegroundColor Cyan
        $sessionConfig | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        Write-Progress "Configuration content validation" "Success" "Sessions config found and readable"
    } catch {
        Write-Progress "Configuration content validation" "Failed" $_.Exception.Message
    }
} else {
    Write-Progress "session_memory_config.yml check" "Failed" "Configuration file missing"
}

# Phase 3: Test Session Creation
Write-Host "`n=== Phase 3: Test Session Creation ===" -ForegroundColor Yellow
Write-Progress "Test session creation phase" "Started"

try {
    $testSessionId = "comprehensive-test-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $sessionDir = "chat_sessions\$testSessionId"
    New-Item -ItemType Directory -Path $sessionDir -Force | Out-Null
    
    $testSessionData = @{
        id = $testSessionId
        created = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        messages = @(
            @{
                role = "user"
                content = "Hello, my name is Zhang San, this is a test session"
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            },
            @{
                role = "assistant"
                content = "Hello Zhang San! I am CodeAssist Pro assistant, I will remember our conversation."
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            }
        )
        metadata = @{
            version = "1.0"
            lastAccessed = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            testType = "comprehensive"
        }
    }
    
    $testSessionData | ConvertTo-Json -Depth 10 | Out-File -FilePath "$sessionDir\session_context.json" -Encoding UTF8
    Write-Progress "Test session creation" "Success" "Session ID: $testSessionId"
    
} catch {
    Write-Progress "Test session creation" "Failed" $_.Exception.Message
}

# Phase 4: File Permissions Check
Write-Host "`n=== Phase 4: File Permissions Check ===" -ForegroundColor Yellow
Write-Progress "File permissions check phase" "Started"

try {
    $testFile = "chat_sessions\permission_test.tmp"
    "permission test" | Out-File -FilePath $testFile -Encoding UTF8
    $content = Get-Content $testFile
    Remove-Item $testFile -Force
    Write-Progress "File write/read permissions" "Success" "Read/write operations successful"
} catch {
    Write-Progress "File write/read permissions" "Failed" $_.Exception.Message
}

# Phase 5: Service Startup Preparation
Write-Host "`n=== Phase 5: Service Startup Preparation ===" -ForegroundColor Yellow
Write-Progress "Service startup preparation" "Started"

$exeFiles = @(
    "..\CodeAssistPro_Simple.exe",
    "..\acli.exe"
)

foreach ($exe in $exeFiles) {
    if (Test-Path $exe) {
        Write-Progress "Executable check: $(Split-Path $exe -Leaf)" "Success" "File exists: $exe"
    } else {
        Write-Progress "Executable check: $(Split-Path $exe -Leaf)" "Failed" "File not found: $exe"
    }
}

# Phase 6: Test Summary
Write-Host "`n=== Phase 6: Test Summary ===" -ForegroundColor Yellow
Write-Progress "Test summary phase" "Started"

Write-Host "`nNext Steps:" -ForegroundColor Green
Write-Host "1. If all checks passed, start the service with:" -ForegroundColor Cyan
Write-Host "   ..\CodeAssistPro_Simple.exe" -ForegroundColor White
Write-Host "   OR" -ForegroundColor Cyan
Write-Host "   ..\acli.exe session_memory_config.yml" -ForegroundColor White
Write-Host "`n2. Test Chinese conversation memory:" -ForegroundColor Cyan
Write-Host "   - Send: 'Hello, my name is Zhang San'" -ForegroundColor White
Write-Host "   - Send: 'What did I say my name was?'" -ForegroundColor White
Write-Host "`n3. Check progress.txt for detailed logs" -ForegroundColor Cyan

Write-Progress "Comprehensive test completed" "Success" "All phases executed, check individual results above"

Write-Host "`n=== Test Completed ===" -ForegroundColor Green
Write-Host "Check progress.txt for detailed execution log" -ForegroundColor Yellow