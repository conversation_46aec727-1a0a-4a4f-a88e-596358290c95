C:\test\chatsessiontest>C:\test\acli.exe rovodev run --config-file session_memory_config.yml "你好"

███████  ██████  ██    ██  ██████      ██████  ███████ ██    ██
██   ██ ██    ██ ██    ██ ██    ██     ██   ██ ██      ██    ██
██████  ██    ██ ██    ██ ██    ██     ██   ██ █████   ██    ██
██   ██ ██    ██  ██  ██  ██    ██     ██   ██ ██       ██  ██
██   ██  ██████    ████    ██████      ██████  ███████   ████

Welcome to Rovo Dev (beta), Atlassian's AI coding agent.

Here are some quick tips:

• Ask Rovo Dev anything in your own words - from "explain this repo" to "add unit tests".
• Type "/" at any time to see available commands.
• Use CTRL+C to interrupt the agent during generation.
• Use /exit to quit.

Working in C:\test\chatsessiontest


╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ An unexpected error occurred, exiting.                                                                               │
╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯

╭──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ To resume your session, restart Rovo Dev CLI with the --restore flag.       