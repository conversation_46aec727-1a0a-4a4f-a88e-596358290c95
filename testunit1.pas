unit testunit1;

{$mode objfpc}{$H+}
{$codepage utf8}

interface

uses
  Classes, SysUtils, Forms, Controls, Graphics, Dialogs, StdCtrls, ExtCtrls,
  Process, LazUTF8,SessionUtils;

type
  TForm1 = class(TForm)
  private
    ChatMemo: TMemo;
    InputEdit: TEdit;
    SendButton: TButton;
    StatusButton: TButton;
    StatusLabel: TLabel;
    InitTimer: TTimer;

    SessionDir: string;
    ConfigFile: string;
    ChatCount: Integer;

    // 认证状态相关
    UserEmail: string;
    AuthStatus: string;
    IsAuthenticated: Boolean;

    procedure CreateGUI;
    procedure SendButtonClick(Sender: TObject);
    procedure StatusButtonClick(Sender: TObject);
    procedure InputEditKeyPress(Sender: TObject; var Key: char);
    procedure FormShow(Sender: TObject);
    procedure InitTimerTimer(Sender: TObject);
    function InitSession: Boolean;
    function SendToAI(const question: string): string;
    function CheckAuthStatus: string;
    procedure PerformInitialAuthCheck;
    procedure UpdateWindowTitle;

    procedure AddChatMessage(const speaker: string; const message: string);
    procedure ShowStatus(const status: string);

  public
    constructor Create(AOwner: TComponent); override;
  end;

var
  Form1: TForm1;

implementation

{$R *.lfm}

procedure WriteLog(const msg: string);
var
  logFile: TextFile;
  timeStr: string;
begin
  try
    timeStr := FormatDateTime('yyyy-mm-dd hh:nn:ss', Now);
    AssignFile(logFile, 'gui_debug.log');
    if FileExists('gui_debug.log') then
      Append(logFile)
    else
      Rewrite(logFile);
    WriteLn(logFile, Format('[%s] %s', [timeStr, msg]));
    CloseFile(logFile);
  except
    // 忽略日志错误
  end;
end;



constructor TForm1.Create(AOwner: TComponent);
begin
  WriteLog('=== GUI 程序启动 ===');
  WriteLog('开始创建窗体');

  inherited Create(AOwner);

  WriteLog('设置窗体属性');
  Caption := 'RovoDev 对话界面 - 正在检查认证状态...';
  Width := 600;
  Height := 400;
  Position := poScreenCenter;

  // 初始化认证状态变量
  UserEmail := '';
  AuthStatus := '未知';
  IsAuthenticated := False;

  // 设置窗体显示事件
  OnShow := @FormShow;

  WriteLog('创建 GUI 控件');
  CreateGUI;

  WriteLog('创建初始化定时器');
  InitTimer := TTimer.Create(Self);
  InitTimer.Interval := 1000; // 1秒后执行
  InitTimer.Enabled := False;  // 先禁用
  InitTimer.OnTimer := @InitTimerTimer;

  WriteLog('初始化变量');
  ChatCount := 0;

  WriteLog('开始初始化会话');
  if InitSession then
  begin
    WriteLog('会话初始化成功');
    ShowStatus('✅ 初始化成功');
    AddChatMessage('系统', '🎉 欢迎使用 RovoDev 对话界面！');
    AddChatMessage('系统', '');
    AddChatMessage('系统', '📋 系统配置信息:');
    AddChatMessage('系统', '  📄 配置文件: ' + ConfigFile);
    AddChatMessage('系统', '  📁 会话目录: C:\test\chatsessiontest\fresh_sessions');
    AddChatMessage('系统', '  🆔 当前会话: ' + ExtractFileName(SessionDir));
    AddChatMessage('系统', '  📝 ACLI日志: C:\test\chatsessiontest\testproj1-acli.log');
    AddChatMessage('系统', '  🏠 工作目录: ' + GetCurrentDir);
    AddChatMessage('系统', '');
    AddChatMessage('系统', '💡 提示: 输入问题后点击"发送"或按回车键开始对话');
    WriteLog('界面初始化完成');
  end
  else
  begin
    WriteLog('会话初始化失败');
    ShowStatus('❌ 初始化失败');
    AddChatMessage('系统', '❌ 初始化失败信息:');
    AddChatMessage('系统', '  📄 配置文件: ' + ConfigFile);
    AddChatMessage('系统', '  📁 会话目录: C:\test\chatsessiontest\fresh_sessions');
    AddChatMessage('系统', '  📝 ACLI日志: C:\test\chatsessiontest\testproj1-acli.log');
    AddChatMessage('系统', '  🏠 工作目录: ' + GetCurrentDir);
    SendButton.Enabled := False;
  end;

  WriteLog('启动自动认证检查定时器');
  InitTimer.Enabled := True; // 启动定时器进行自动认证检查

  WriteLog('=== GUI 程序启动完成 ===');
end;

procedure TForm1.CreateGUI;
begin
  WriteLog('开始创建 GUI 控件');

  // 聊天显示区
  WriteLog('创建聊天显示区');
  ChatMemo := TMemo.Create(Self);
  ChatMemo.Parent := Self;
  ChatMemo.Left := 10;
  ChatMemo.Top := 10;
  ChatMemo.Width := ClientWidth - 20;
  ChatMemo.Height := ClientHeight - 80;
  ChatMemo.Anchors := [akLeft, akTop, akRight, akBottom];
  ChatMemo.ReadOnly := True;
  ChatMemo.ScrollBars := ssVertical;
  // 设置字体支持 Unicode，尝试多种字体
  if Screen.Fonts.IndexOf('Segoe UI Emoji') >= 0 then
    ChatMemo.Font.Name := 'Segoe UI Emoji'
  else if Screen.Fonts.IndexOf('Microsoft YaHei') >= 0 then
    ChatMemo.Font.Name := 'Microsoft YaHei'
  else if Screen.Fonts.IndexOf('SimSun') >= 0 then
    ChatMemo.Font.Name := 'SimSun'
  else
    ChatMemo.Font.Name := 'Arial Unicode MS';
  ChatMemo.Font.Size := 10;

  // 输入框
  InputEdit := TEdit.Create(Self);
  InputEdit.Parent := Self;
  InputEdit.Left := 10;
  InputEdit.Top := ChatMemo.Top + ChatMemo.Height + 10;
  InputEdit.Width := ClientWidth - 145;  // 为两个按钮腾出空间
  InputEdit.Height := 25;
  InputEdit.Anchors := [akLeft, akBottom, akRight];
  InputEdit.OnKeyPress := @InputEditKeyPress;

  // 发送按钮
  SendButton := TButton.Create(Self);
  SendButton.Parent := Self;
  SendButton.Left := InputEdit.Left + InputEdit.Width + 10;
  SendButton.Top := InputEdit.Top;
  SendButton.Width := 60;
  SendButton.Height := 25;
  SendButton.Caption := '发送';
  SendButton.Anchors := [akRight, akBottom];
  SendButton.OnClick := @SendButtonClick;

  // 状态按钮
  StatusButton := TButton.Create(Self);
  StatusButton.Parent := Self;
  StatusButton.Left := SendButton.Left + SendButton.Width + 5;
  StatusButton.Top := InputEdit.Top;
  StatusButton.Width := 60;
  StatusButton.Height := 25;
  StatusButton.Caption := '状态';
  StatusButton.Anchors := [akRight, akBottom];
  StatusButton.OnClick := @StatusButtonClick;

  // 状态标签
  StatusLabel := TLabel.Create(Self);
  StatusLabel.Parent := Self;
  StatusLabel.Left := 10;
  StatusLabel.Top := InputEdit.Top + InputEdit.Height + 5;
  StatusLabel.Width := ClientWidth - 20;
  StatusLabel.Caption := '正在初始化...';
  StatusLabel.Anchors := [akLeft, akBottom, akRight];

  WriteLog('GUI 控件创建完成');
end;

function TForm1.InitSession: Boolean;
var
  SearchRec: TSearchRec;
  LatestTime: TDateTime;
  LatestDir: string;
  CurrentTime: TDateTime;
  SessionsDir: string;
  SessionFile: string;
begin
  WriteLog('开始初始化会话');
  Result := False;

  try
    WriteLog('设置配置文件路径');
    ConfigFile := 'fresh_session_config.yml';

    WriteLog('检查配置文件: ' + ConfigFile);
    if not FileExists(ConfigFile) then
    begin
      WriteLog('配置文件不存在');
      ShowStatus('❌ 配置文件不存在');
      Exit;
    end;

    WriteLog('检查 acli.exe');
    // 智能查找 acli.exe 位置
    if FileExists('acli.exe') then
      WriteLog('找到 acli.exe 在当前目录')
    else if FileExists('..\acli.exe') then
      WriteLog('找到 acli.exe 在上级目录')
    else if FileExists('testacl\acli.exe') then
      WriteLog('找到 acli.exe 在 testacl 目录')
    else
    begin
      WriteLog('acli.exe 不存在于任何预期位置');
      ShowStatus('⚠️ acli.exe 未找到，但继续初始化');
      // 不退出，继续初始化
    end;

    // 从配置文件读取会话目录设置
    WriteLog('读取配置文件中的会话目录设置');
    // 使用配置文件中的绝对路径
    SessionsDir := 'C:\test\chatsessiontest\fresh_sessions';
    WriteLog('使用会话目录: ' + SessionsDir);
    LatestTime := 0;
    LatestDir := '';

    if FindFirst(SessionsDir + '\*', faDirectory, SearchRec) = 0 then
    begin
      repeat
        if (SearchRec.Name <> '.') and (SearchRec.Name <> '..') then
        begin
          // 检查会话文件的修改时间，而不是目录的修改时间
          SessionFile := IncludeTrailingPathDelimiter(SessionsDir) + SearchRec.Name + '\session_context.json';
          if FileExists(SessionFile) then
          begin
            CurrentTime := FileDateToDateTime(FileAge(SessionFile));
            WriteLog('检查会话: ' + SearchRec.Name + ', 文件时间: ' + DateTimeToStr(CurrentTime));
            if CurrentTime > LatestTime then
            begin
              LatestTime := CurrentTime;
              LatestDir := SearchRec.Name;
              WriteLog('更新最新会话: ' + LatestDir + ', 时间: ' + DateTimeToStr(LatestTime));
            end;
          end;
        end;
      until FindNext(SearchRec) <> 0;
      FindClose(SearchRec);
    end;

    if LatestDir <> '' then
    begin
      SessionDir := IncludeTrailingPathDelimiter(SessionsDir) + LatestDir;
      WriteLog('设置会话目录: ' + SessionDir);
      Result := True;
    end
    else
    begin
      WriteLog('未找到任何会话目录');
      Result := False;
    end;

  except
    on E: Exception do
      ShowStatus('❌ 错误: ' + E.Message);
  end;
end;

function TForm1.CheckAuthStatus: string;
var
  Process: TProcess;
  OutputLines: TStringList;
  i: Integer;
  Line: string;
  ExitCode: Integer;
  WaitCount: Integer;
begin
  WriteLog('=== 开始检查认证状态 ===');
  Result := '';

  // 重置认证状态变量
  UserEmail := '';
  AuthStatus := '检查中...';
  IsAuthenticated := False;

  try
    // 创建 TProcess 来执行认证状态检查
    Process := TProcess.Create(nil);
    OutputLines := TStringList.Create;
    try
      WriteLog('设置 Process 参数');
      Process.Executable :=  '..\acli.exe';
      Process.Parameters.Add('rovodev');
      Process.Parameters.Add('auth');
      Process.Parameters.Add('status');

      WriteLog('设置 Process 选项');
      Process.ShowWindow := swoHide;
      Process.Options := [poWaitOnExit, poNoConsole, poUsePipes];

      WriteLog('开始执行认证状态检查');
      Process.Execute;

      // 等待进程完成，最多等待 30 秒
      WaitCount := 0;
      while Process.Running and (WaitCount < 300) do  // 30秒 = 300 * 100ms
      begin
        Sleep(100);
        Application.ProcessMessages;
        Inc(WaitCount);
      end;

      if Process.Running then
      begin
        WriteLog('认证状态检查超时，强制终止');
        Process.Terminate(1);
        ExitCode := -2;
        Result := '❌ 检查超时';
        AuthStatus := '检查超时';
      end
      else
      begin
        ExitCode := Process.ExitStatus;
        WriteLog('认证状态检查完成，退出代码: ' + IntToStr(ExitCode));

        // 读取输出 - 使用更安全的方式
        try
          while Process.Output.NumBytesAvailable > 0 do
          begin
            SetLength(Line, Process.Output.NumBytesAvailable);
            Process.Output.Read(Line[1], Length(Line));
            OutputLines.Add(Line);
          end;

          WriteLog('获取到输出，行数: ' + IntToStr(OutputLines.Count));

          // 解析输出内容并提取用户信息
          for i := 0 to OutputLines.Count - 1 do
          begin
            Line := Trim(OutputLines[i]);
            WriteLog('输出行 ' + IntToStr(i) + ': ' + Line);
            if Line <> '' then
            begin
              if Result <> '' then
                Result := Result + #13#10;
              Result := Result + Line;

              // 解析认证状态
              if Pos('✓ Authenticated', Line) > 0 then
              begin
                IsAuthenticated := True;
                AuthStatus := '已认证';
              end
              else if (Pos('Email:', Line) > 0) or (Pos(' Email:', Line) > 0) then
              begin
                // 提取邮箱地址
                UserEmail := Trim(Copy(Line, Pos(':', Line) + 1, Length(Line)));
                WriteLog('提取到用户邮箱: ' + UserEmail);
              end;
            end;
          end;
        except
          on E: Exception do
          begin
            WriteLog('读取输出时出错: ' + E.Message);
            // 继续执行，不中断
          end;
        end;

        if Result = '' then
        begin
          if ExitCode = 0 then
          begin
            Result := '✅ 已认证 (无详细信息)';
            IsAuthenticated := True;
            AuthStatus := '已认证';
          end
          else
          begin
            Result := '❌ 未认证或认证失败 (退出代码: ' + IntToStr(ExitCode) + ')';
            IsAuthenticated := False;
            AuthStatus := '未认证';
          end;
        end
        else
        begin
          // 如果没有检测到认证状态，根据退出代码判断
          if not IsAuthenticated and (ExitCode <> 0) then
          begin
            IsAuthenticated := False;
            AuthStatus := '未认证';
          end;
        end;
      end;

    finally
      OutputLines.Free;
      Process.Free;
    end;

  except
    on E: Exception do
    begin
      WriteLog('检查认证状态时出错: ' + E.Message);
      Result := '❌ 检查失败: ' + E.Message;
      IsAuthenticated := False;
      AuthStatus := '检查失败';
    end;
  end;

  WriteLog('认证状态检查结果: ' + Result);
  WriteLog('用户邮箱: ' + UserEmail);
  WriteLog('认证状态: ' + AuthStatus);
  WriteLog('是否已认证: ' + BoolToStr(IsAuthenticated, True));

  // 更新窗体标题
  UpdateWindowTitle;

  WriteLog('=== 认证状态检查完成 ===');
end;

function TForm1.SendToAI(const question: string): string;
var
  Process: TProcess;
  ExitCode: Integer;
  WaitCount: Integer;
  AbsSessionDir: string;
  MessagePair: TMessagePair;
  SessionFile: string;
  SessionInfo: TSessionInfo;
  OriginalTime, CurrentTime: TDateTime;
  FileUpdated: Boolean;
  i: Integer;
  CommandLine: string;
  ParamStr: string;
begin
  WriteLog('进入 SendToAI 方法，问题: ' + question);
  Result := '';

  try
    WriteLog('显示发送状态');
    ShowStatus('⏳ 发送问题...');
    Application.ProcessMessages;

    // 使用 TProcess 隐藏终端窗口
    WriteLog('创建 TProcess');
    Process := TProcess.Create(nil);
    try
      WriteLog('设置 Process 参数');
      // 智能查找 acli.exe 位置
      if FileExists('acli.exe') then
        Process.Executable := 'acli.exe'
      else if FileExists('..\acli.exe') then
        Process.Executable := '..\acli.exe'
      else if FileExists('testacl\acli.exe') then
        Process.Executable := 'testacl\acli.exe'
      else
      begin
        WriteLog('❌ 无法找到 acli.exe');
        ShowStatus('❌ acli.exe 不存在');
        Exit('');
      end;
      
      WriteLog('使用 acli.exe 路径: ' + Process.Executable);
      Process.Parameters.Add('rovodev');
      Process.Parameters.Add('run');
      Process.Parameters.Add('--config-file');
      // 使用绝对路径传递配置文件
      Process.Parameters.Add(IncludeTrailingPathDelimiter(GetCurrentDir) + ConfigFile);
      WriteLog('使用配置文件路径: ' + IncludeTrailingPathDelimiter(GetCurrentDir) + ConfigFile);
      WriteLog('将通过标准输入发送问题: ' + question);

      WriteLog('设置 Process 选项');
      // 隐藏窗口并重定向输出
      Process.ShowWindow := swoHide;
      Process.Options := [poWaitOnExit, poNoConsole, poUsePipes];
      // 设置工作目录为当前目录，确保 acli 在正确的位置创建会话
      Process.CurrentDirectory := GetCurrentDir;
      WriteLog('设置工作目录: ' + GetCurrentDir);

      // 输出完整的执行命令到日志
      WriteLog('准备执行命令:');
      WriteLog('  可执行文件: ' + Process.Executable);
      WriteLog('  工作目录: ' + Process.CurrentDirectory);
      WriteLog('  参数列表:');
      for i := 0 to Process.Parameters.Count - 1 do
        WriteLog('    [' + IntToStr(i) + '] ' + Process.Parameters[i]);
      // 构建完整命令字符串（简化版本）
      WriteLog('完整命令: ' + Process.Executable + ' rovodev run --config-file "' + 
               IncludeTrailingPathDelimiter(GetCurrentDir) + ConfigFile + '" "' + question + '"');
      
      WriteLog('开始执行 Process (流式模式)');
      try
        // 对于流式响应，使用不同的策略
        Process.Options := [poNoConsole, poUsePipes];
        Process.Execute;
        WriteLog('Process 启动成功，开始流式处理...');

        // 通过标准输入发送问题
        WriteLog('流式进程已启动，进程ID: ' + IntToStr(Process.ProcessID));

        // 对于流式响应，我们启动进程后立即返回，让它在后台运行
        WriteLog('流式进程已启动，进程ID: ' + IntToStr(Process.ProcessID));
        
        WriteLog('流式进程已启动，进程ID: ' + IntToStr(Process.ProcessID));
        
        ExitCode := 0; // 假设启动成功
      except
        on E: Exception do
        begin
          WriteLog('Process 执行异常: ' + E.Message);
          ExitCode := -1;
        end;
      end;
    finally
      Process.Free;
    end;

    if ExitCode <> 0 then
    begin
      ShowStatus('❌ 发送失败，退出代码: ' + IntToStr(ExitCode));
      Exit;
    end;

    ShowStatus('⏳ 等待流式响应...');
    Application.ProcessMessages;

    // 对于流式响应，我们需要等待会话文件更新
    // 检查会话文件的修改时间，等待其更新
    WriteLog('开始等待会话文件更新...');
    WaitCount := 0;
    SessionFile := IncludeTrailingPathDelimiter(SessionDir) + 'session_context.json';
    
    // 记录当前文件的修改时间
    if FileExists(SessionFile) then
    begin
      OriginalTime := FileDateToDateTime(FileAge(SessionFile));
      WriteLog('原始文件时间: ' + DateTimeToStr(OriginalTime));
    end
    else
    begin
      OriginalTime := 0;
      WriteLog('会话文件不存在，等待创建');
    end;
    
    // 等待文件更新，最多等待 30 秒
    FileUpdated := False;
    while WaitCount < 300 do  // 30秒 = 300 * 100ms
    begin
      Sleep(100);
      Application.ProcessMessages;
      Inc(WaitCount);
      
      if FileExists(SessionFile) then
      begin
        CurrentTime := FileDateToDateTime(FileAge(SessionFile));
        if CurrentTime > OriginalTime then
        begin
          WriteLog('检测到会话文件更新: ' + DateTimeToStr(CurrentTime));
          FileUpdated := True;
          Break;
        end;
      end;
      
      if WaitCount mod 50 = 0 then  // 每5秒记录一次
        WriteLog('等待会话文件更新... ' + IntToStr(WaitCount div 10) + '秒');
    end;
    
    if not FileUpdated then
    begin
      WriteLog('❌ 超时：30秒内未检测到会话文件更新');
      ShowStatus('❌ AI响应超时');
      Exit('');
    end;

    ShowStatus('⏳ 正在处理会话: ' + ExtractFileName(SessionDir));
    Application.ProcessMessages;

    // 逐步测试 SessionUtils 的各个函数，找出具体哪一步失败
    try
      WriteLog('开始逐步测试 SessionUtils 函数');
      WriteLog('会话目录: ' + SessionDir);

      // 步骤1：检查会话文件是否存在
      SessionFile := SessionDir + '\session_context.json';
      WriteLog('步骤1：检查会话文件: ' + SessionFile);
      if not FileExists(SessionFile) then
      begin
        WriteLog('❌ 会话文件不存在');
        AddChatMessage('系统', '❌ 会话文件不存在');
        Result := '';
        Exit;
      end;
      WriteLog('✅ 会话文件存在');
      AddChatMessage('系统', '✅ 会话文件存在');

      // 步骤2：测试 GetSessionInfo
      WriteLog('步骤2：测试 GetSessionInfo');
      try
        SessionInfo := GetSessionInfo(SessionDir);
        if SessionInfo.IsValid then
        begin
          WriteLog('✅ GetSessionInfo 成功');
          WriteLog('会话ID: ' + SessionInfo.SessionID);
          WriteLog('消息数量: ' + IntToStr(SessionInfo.MessageCount));
          AddChatMessage('系统', '✅ GetSessionInfo 成功，消息数量: ' + IntToStr(SessionInfo.MessageCount));
        end
        else
        begin
          WriteLog('❌ GetSessionInfo 返回无效结果');
          AddChatMessage('系统', '❌ GetSessionInfo 失败');
        end;
      except
        on E: Exception do
        begin
          WriteLog('❌ GetSessionInfo 异常: ' + E.Message);
          AddChatMessage('系统', '❌ GetSessionInfo 异常: ' + E.Message);
        end;
      end;

      // 步骤3：测试 ExtractLatestResponseFromJSON
      WriteLog('步骤3：测试 ExtractLatestResponseFromJSON');
      try
        Result := ExtractLatestResponseFromJSON(SessionFile);
        if Result <> '' then
        begin
          WriteLog('✅ ExtractLatestResponseFromJSON 成功，回复长度: ' + IntToStr(Length(Result)));
          AddChatMessage('系统', '✅ 成功提取回复，长度: ' + IntToStr(Length(Result)));
        end
        else
        begin
          WriteLog('❌ ExtractLatestResponseFromJSON 返回空结果');
          AddChatMessage('系统', '❌ 提取回复失败');
        end;
      except
        on E: Exception do
        begin
          WriteLog('❌ ExtractLatestResponseFromJSON 异常: ' + E.Message);
          AddChatMessage('系统', '❌ 提取回复异常: ' + E.Message);
          Result := '';
        end;
      end;

    except
      on E: Exception do
      begin
        WriteLog('总体异常: ' + E.Message);
        AddChatMessage('系统', '❌ 总体异常: ' + E.Message);
        AddChatMessage('系统', '错误类型: ' + E.ClassName);
        Result := '';
      end;
    end;

    if Result <> '' then
    begin
      ShowStatus('✅ 完成，回复长度: ' + IntToStr(Length(Result)));
    end
    else
    begin
      ShowStatus('❌ 获取回复失败');
      AddChatMessage('系统', '错误: 无法从会话目录获取回复');
    end;

  except
    on E: Exception do
    begin
      ShowStatus('❌ 错误: ' + E.Message);
      AddChatMessage('系统', '详细错误信息: ' + E.Message);
      AddChatMessage('系统', '错误类型: ' + E.ClassName);
    end;
  end;
end;



procedure TForm1.AddChatMessage(const speaker: string; const message: string);
var
  timeStr: string;
  prefix: string;
begin
  timeStr := FormatDateTime('hh:nn:ss', Now);

  case speaker of
    '系统': prefix := '🔧 系统';
    '您': prefix := '👤 您';
    'AI': prefix := '🤖 AI';
    else prefix := speaker;
  end;

  ChatMemo.Lines.Add(Format('[%s] %s: %s', [timeStr, prefix, message]));
  ChatMemo.Lines.Add('');

  // 滚动到底部
  ChatMemo.SelStart := Length(ChatMemo.Text);
  ChatMemo.SelLength := 0;
end;

procedure TForm1.ShowStatus(const status: string);
begin
  StatusLabel.Caption := status;
  Application.ProcessMessages;
end;

procedure TForm1.SendButtonClick(Sender: TObject);
var
  question: string;
  response: string;
begin
  WriteLog('=== 开始发送问题 ===');
  question := Trim(InputEdit.Text);
  WriteLog('用户问题: ' + question);

  if question = '' then
  begin
    ShowMessage('请输入问题！');
    InputEdit.SetFocus;
    Exit;
  end;

  WriteLog('禁用按钮和输入框');
  SendButton.Enabled := False;
  InputEdit.Enabled := False;

  try
    Inc(ChatCount);
    WriteLog('聊天计数: ' + IntToStr(ChatCount));

    AddChatMessage('您', question);
    WriteLog('开始调用 SendToAI');

    response := SendToAI(question);
    WriteLog('SendToAI 返回，回复长度: ' + IntToStr(Length(response)));

    if response <> '' then
    begin
      AddChatMessage('AI', response);
      ShowStatus(Format('✅ 第 %d 轮对话完成', [ChatCount]));
    end
    else
    begin
      AddChatMessage('系统', '❌ 获取回复失败');
      Dec(ChatCount);
    end;

  finally
    SendButton.Enabled := True;
    InputEdit.Enabled := True;
    InputEdit.Text := '';
    InputEdit.SetFocus;
  end;
end;

procedure TForm1.StatusButtonClick(Sender: TObject);
var
  authResult: string;
begin
  WriteLog('=== 开始状态检查 ===');

  // 禁用按钮防止重复点击
  StatusButton.Enabled := False;
  SendButton.Enabled := False;

  try
    ShowStatus('⏳ 正在检查认证状态...');
    Application.ProcessMessages;

    AddChatMessage('系统', '正在检查 RovoDev 认证状态...');

    authResult := CheckAuthStatus;

    if authResult <> '' then
    begin
      AddChatMessage('系统', '认证状态检查结果:');
      AddChatMessage('系统', authResult);

      if Pos('✅', authResult) > 0 then
        ShowStatus('✅ 认证状态: 已认证')
      else if Pos('❌', authResult) > 0 then
        ShowStatus('❌ 认证状态: 未认证或失败')
      else
        ShowStatus('ℹ️ 认证状态: ' + Copy(authResult, 1, 30));
    end
    else
    begin
      AddChatMessage('系统', '❌ 无法获取认证状态');
      ShowStatus('❌ 状态检查失败');
    end;

  finally
    StatusButton.Enabled := True;
    SendButton.Enabled := True;
  end;

  WriteLog('=== 状态检查完成 ===');
end;

procedure TForm1.FormShow(Sender: TObject);
begin
  WriteLog('=== 窗体显示事件触发 ===');
  // 窗体显示后自动进行认证检查
  PerformInitialAuthCheck;
end;

procedure TForm1.InitTimerTimer(Sender: TObject);
begin
  WriteLog('=== 定时器触发，开始自动认证检查 ===');

  // 禁用定时器，只执行一次
  InitTimer.Enabled := False;

  // 执行自动认证检查
  PerformInitialAuthCheck;
end;

procedure TForm1.PerformInitialAuthCheck;
var
  authResult: string;
begin
  WriteLog('=== 开始自动认证检查 ===');

  try
    AddChatMessage('系统', '正在自动检查 RovoDev 认证状态...');
    ShowStatus('⏳ 检查认证状态...');
    Application.ProcessMessages;

    WriteLog('调用 CheckAuthStatus');
    authResult := CheckAuthStatus;
    WriteLog('CheckAuthStatus 返回: ' + authResult);

    if authResult <> '' then
    begin
      AddChatMessage('系统', '认证状态检查完成:');
      AddChatMessage('系统', authResult);

      if Pos('✓', authResult) > 0 then
      begin
        ShowStatus('✅ 已认证 - 可以开始对话');
        AddChatMessage('系统', '✅ 认证正常，您可以开始与 AI 对话了！');
        WriteLog('认证状态：已认证');
      end
      else if Pos('❌', authResult) > 0 then
      begin
        ShowStatus('❌ 未认证 - 请先登录');
        AddChatMessage('系统', '❌ 未认证，请先运行以下命令进行登录：');
        AddChatMessage('系统', '   acli rovodev auth login');
        AddChatMessage('系统', '或者点击"状态"按钮重新检查认证状态');
        WriteLog('认证状态：未认证');
      end
      else
      begin
        ShowStatus('ℹ️ 认证状态未知');
        AddChatMessage('系统', 'ℹ️ 认证状态信息不明确，建议点击"状态"按钮重新检查');
        WriteLog('认证状态：未知');
      end;
    end
    else
    begin
      ShowStatus('❌ 状态检查失败');
      AddChatMessage('系统', '❌ 无法获取认证状态，可能的原因：');
      AddChatMessage('系统', '- 网络连接问题');
      AddChatMessage('系统', '- acli.exe 不存在或无法执行');
      AddChatMessage('系统', '您可以点击"状态"按钮重新尝试检查');
      WriteLog('认证状态检查失败');
    end;

  except
    on E: Exception do
    begin
      ShowStatus('❌ 检查出错');
      AddChatMessage('系统', '❌ 认证状态检查出错: ' + E.Message);
      AddChatMessage('系统', '您可以点击"状态"按钮重新尝试检查');
      WriteLog('认证状态检查异常: ' + E.Message);
    end;
  end;

  AddChatMessage('系统', ''); // 添加空行分隔
  WriteLog('=== 自动认证检查完成 ===');
end;

procedure TForm1.UpdateWindowTitle;
var
  titleText: string;
begin
  WriteLog('=== 更新窗体标题 ===');

  titleText := 'RovoDev 对话界面';

  if IsAuthenticated then
  begin
    if UserEmail <> '' then
      titleText := titleText + ' - ✅ ' + UserEmail + ' (已认证)'
    else
      titleText := titleText + ' - ✅ 已认证';
  end
  else
  begin
    if AuthStatus <> '' then
      titleText := titleText + ' - ❌ ' + AuthStatus
    else
      titleText := titleText + ' - ❌ 未认证';
  end;

  Caption := titleText;
  WriteLog('窗体标题已更新: ' + titleText);
end;

procedure TForm1.InputEditKeyPress(Sender: TObject; var Key: char);
begin
  if Key = #13 then // 回车发送
  begin
    Key := #0;
    SendButtonClick(nil);
  end;
end;

end.
