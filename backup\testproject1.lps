<?xml version="1.0" encoding="UTF-8"?>
<CONFIG>
  <ProjectSession>
    <PathDelim Value="\"/>
    <Version Value="12"/>
    <BuildModes Active="Default"/>
    <Units>
      <Unit>
        <Filename Value="testproject1.lpr"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="4"/>
        <TopLine Value="10"/>
        <UsageCount Value="21"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="testunit1.pas"/>
        <IsPartOfProject Value="True"/>
        <ComponentName Value="Form1"/>
        <HasResources Value="True"/>
        <ResourceBaseClass Value="Form"/>
        <IsVisibleTab Value="True"/>
        <TopLine Value="679"/>
        <CursorPos X="16" Y="686"/>
        <UsageCount Value="21"/>
        <Loaded Value="True"/>
        <LoadedDesigner Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="SessionUtils.pas"/>
        <IsPartOfProject Value="True"/>
        <EditorIndex Value="2"/>
        <CursorPos X="18"/>
        <UsageCount Value="20"/>
        <Loaded Value="True"/>
      </Unit>
      <Unit>
        <Filename Value="custom_persistence_config.yml"/>
        <EditorIndex Value="1"/>
        <CursorPos X="19" Y="15"/>
        <UsageCount Value="20"/>
        <Loaded Value="True"/>
        <DefaultSyntaxHighlighter Value="None"/>
      </Unit>
      <Unit>
        <Filename Value="C:\lazarus\fpc\3.2.2\source\packages\fcl-process\src\process.pp"/>
        <EditorIndex Value="3"/>
        <UsageCount Value="10"/>
        <Loaded Value="True"/>
      </Unit>
    </Units>
    <JumpHistory HistoryIndex="19">
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="167" Column="16" TopLine="155"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="165" Column="42"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="19"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="8"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="22" Column="12" TopLine="7"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="37" Column="25" TopLine="24"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="112" Column="17" TopLine="98"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="119" Column="58" TopLine="105"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="209" Column="23" TopLine="194"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="443" Column="27" TopLine="429"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="10" Column="32"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="295" Column="33" TopLine="290"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="452" Column="40" TopLine="443"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="23" Column="15" TopLine="13"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="118" Column="61" TopLine="104"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="128" Column="61" TopLine="114"/>
      </Position>
      <Position>
        <Filename Value="custom_persistence_config.yml"/>
        <Caret Line="15" Column="19"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="665" Column="17" TopLine="658"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="682" Column="3" TopLine="674"/>
      </Position>
      <Position>
        <Filename Value="testunit1.pas"/>
        <Caret Line="686" Column="16" TopLine="678"/>
      </Position>
    </JumpHistory>
    <RunParams>
      <FormatVersion Value="2"/>
      <Modes ActiveMode=""/>
    </RunParams>
  </ProjectSession>
</CONFIG>
