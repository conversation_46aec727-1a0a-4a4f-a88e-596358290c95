
C:\test\chatsessiontest>C:\test\acli.exe rovodev run  "你好"

███████  ██████  ██    ██  ██████      ██████  ███████ ██    ██
██   ██ ██    ██ ██    ██ ██    ██     ██   ██ ██      ██    ██
██████  ██    ██ ██    ██ ██    ██     ██   ██ █████   ██    ██
██   ██ ██    ██  ██  ██  ██    ██     ██   ██ ██       ██  ██
██   ██  ██████    ████    ██████      ██████  ███████   ████

Welcome to Rovo Dev (beta), Atlassian's AI coding agent.

Here are some quick tips:

• Ask R<PERSON> Dev anything in your own words - from "explain this repo" to "add unit tests".
• Type "/" at any time to see available commands.
• Use CTRL+C to interrupt the agent during generation.
• Use /exit to quit.

Working in C:\test\chatsessiontest


╭─ Response ───────────────────────────────────────────────────────────────────────────────────────────────────────────╮
│ 你好！我是 Rovo Dev，一个友好且乐于助人的 AI 助手，专门帮助软件开发者完成各种任务。                                  │
│                                                                                                                      │
│ 我可以帮助您：                                                                                                       │
│                                                                                                                      │
│  • 分析和修改代码                                                                                                    │
│  • 调试问题                                                                                                          │
│  • 实现新功能                                                                                                        │
│  • 重构代码                                                                                                          │
│  • 运行测试                                                                                                          │
│  • 查看和编辑工作区中的文件                                                                                          │
│                                                                                                                      │
│ 我注意到您的工作区中有一些文件，包括配置文件、测试脚本和会话相关的内容。请告诉我您需要什么帮助，我会尽力协助您！     │
│                                                                                                                      │
│ 您可以用中文或英文与我交流，我都能理解和回应。                                                                       │
╰──────────────────────────────────────────────────────────────────────────────────────────────────────────────────────╯
