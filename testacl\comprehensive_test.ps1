﻿# CodeAssistPro 浼氳瘽璁板繂鍔熻兘缁煎悎娴嬭瘯鑴氭湰
# 鍖呭惈鑷姩杩涘害璁板綍鍔熻兘
# 鍒涘缓鏃堕棿: 2025-07-13

param(
    [string]$ProgressFile = "progress.txt"
)

# 杩涘害璁板綍鍑芥暟
function Write-Progress {
    param(
        [string]$Message,
        [string]$Status = "In Progress",
        [string]$Details = ""
    )
    
    $timestamp = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $logEntry = "[$timestamp] $Message - $Status"
    if ($Details) {
        $logEntry += "`r`n[$timestamp] $Details"
    }
    $logEntry += "`r`n"
    
    # 鏄剧ず鍒版帶鍒跺彴
    Write-Host "[$timestamp] $Message - $Status" -ForegroundColor $(
        switch ($Status) {
            "Success" { "Green" }
            "Failed" { "Red" }
            "In Progress" { "Yellow" }
            default { "White" }
        }
    )
    if ($Details) {
        Write-Host "[$timestamp] $Details" -ForegroundColor Cyan
    }
    
    # 鍐欏叆杩涘害鏂囦欢
    try {
        Add-Content -Path $ProgressFile -Value $logEntry -Encoding UTF8
    } catch {
        Write-Warning "Failed to write to progress file: $($_.Exception.Message)"
    }
}

# 寮€濮嬫祴璇?
Write-Host "=== CodeAssistPro Session Memory Comprehensive Test ===" -ForegroundColor Green
Write-Progress "Starting comprehensive test" "In Progress"

# 1. 鐜妫€鏌?
Write-Host "`n=== Phase 1: Environment Check ===" -ForegroundColor Yellow
Write-Progress "Environment check phase" "Started"

# 妫€鏌ョ洰褰曠粨鏋?
Write-Host "`nChecking directory structure..." -ForegroundColor Cyan
$currentDir = Get-Location
Write-Progress "Current directory check" "Success" "Location: $currentDir"

# 妫€鏌hat_sessions鐩綍
if (Test-Path "chat_sessions") {
    Write-Progress "chat_sessions directory check" "Success" "Directory exists"
} else {
    Write-Progress "chat_sessions directory check" "Failed" "Directory missing, creating..."
    try {
        New-Item -ItemType Directory -Path "chat_sessions" -Force | Out-Null
        Write-Progress "chat_sessions directory creation" "Success"
    } catch {
        Write-Progress "chat_sessions directory creation" "Failed" $_.Exception.Message
    }
}

# 妫€鏌ogs鐩綍
if (Test-Path "logs") {
    Write-Progress "logs directory check" "Success" "Directory exists"
} else {
    Write-Progress "logs directory check" "Failed" "Directory missing, creating..."
    try {
        New-Item -ItemType Directory -Path "logs" -Force | Out-Null
        Write-Progress "logs directory creation" "Success"
    } catch {
        Write-Progress "logs directory creation" "Failed" $_.Exception.Message
    }
}

# 2. 閰嶇疆鏂囦欢妫€鏌?
Write-Host "`n=== Phase 2: Configuration Check ===" -ForegroundColor Yellow
Write-Progress "Configuration check phase" "Started"

# 妫€鏌ession_memory_config.yml
if (Test-Path "session_memory_config.yml") {
    Write-Progress "session_memory_config.yml check" "Success"
    try {
        $sessionConfig = Get-Content "session_memory_config.yml" | Select-String -A 10 "sessions:"
        Write-Host "Session configuration preview:" -ForegroundColor Cyan
        $sessionConfig | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        Write-Progress "Configuration content validation" "Success" "Sessions config found and readable"
    } catch {
        Write-Progress "Configuration content validation" "Failed" $_.Exception.Message
    }
} else {
    Write-Progress "session_memory_config.yml check" "Failed" "Configuration file missing"
}

# 3. 娴嬭瘯浼氳瘽鍒涘缓
Write-Host "`n=== Phase 3: Test Session Creation ===" -ForegroundColor Yellow
Write-Progress "Test session creation phase" "Started"

try {
    $testSessionId = "comprehensive-test-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    $sessionDir = "chat_sessions\$testSessionId"
    New-Item -ItemType Directory -Path $sessionDir -Force | Out-Null
    
    $testSessionData = @{
        id = $testSessionId
        created = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
        messages = @(
            @{
                role = "user"
                content = "浣犲ソ锛屾垜鍙紶涓夛紝杩欐槸涓€涓祴璇曚細璇?
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            },
            @{
                role = "assistant"
                content = "浣犲ソ寮犱笁锛佹垜鏄疌odeAssist Pro鍔╂墜锛屾垜浼氳浣忔垜浠殑瀵硅瘽銆?
                timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            }
        )
        metadata = @{
            version = "1.0"
            lastAccessed = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            testType = "comprehensive"
        }
    }
    
    $testSessionData | ConvertTo-Json -Depth 10 | Out-File -FilePath "$sessionDir\session_context.json" -Encoding UTF8
    Write-Progress "Test session creation" "Success" "Session ID: $testSessionId"
    
} catch {
    Write-Progress "Test session creation" "Failed" $_.Exception.Message
}

# 4. 鏂囦欢鏉冮檺楠岃瘉
Write-Host "`n=== Phase 4: File Permissions Check ===" -ForegroundColor Yellow
Write-Progress "File permissions check phase" "Started"

try {
    $testFile = "chat_sessions\permission_test.tmp"
    "permission test" | Out-File -FilePath $testFile -Encoding UTF8
    $content = Get-Content $testFile
    Remove-Item $testFile -Force
    Write-Progress "File write/read permissions" "Success" "Read/write operations successful"
} catch {
    Write-Progress "File write/read permissions" "Failed" $_.Exception.Message
}

# 5. 鏈嶅姟鍚姩鍑嗗
Write-Host "`n=== Phase 5: Service Startup Preparation ===" -ForegroundColor Yellow
Write-Progress "Service startup preparation" "Started"

# 妫€鏌ュ彲鎵ц鏂囦欢
$exeFiles = @(
    "..\CodeAssistPro_Simple.exe",
    "..\acli.exe"
)

foreach ($exe in $exeFiles) {
    if (Test-Path $exe) {
        Write-Progress "Executable check: $(Split-Path $exe -Leaf)" "Success" "File exists: $exe"
    } else {
        Write-Progress "Executable check: $(Split-Path $exe -Leaf)" "Failed" "File not found: $exe"
    }
}

# 6. 娴嬭瘯鎬荤粨
Write-Host "`n=== Phase 6: Test Summary ===" -ForegroundColor Yellow
Write-Progress "Test summary phase" "Started"

Write-Host "`nNext Steps:" -ForegroundColor Green
Write-Host "1. If all checks passed, start the service with:" -ForegroundColor Cyan
Write-Host "   ..\CodeAssistPro_Simple.exe" -ForegroundColor White
Write-Host "   OR" -ForegroundColor Cyan
Write-Host "   ..\acli.exe session_memory_config.yml" -ForegroundColor White
Write-Host "`n2. Test Chinese conversation memory:" -ForegroundColor Cyan
Write-Host "   - Send: '浣犲ソ锛屾垜鍙紶涓?" -ForegroundColor White
Write-Host "   - Send: '鎴戝垰鎵嶈鎴戝彨浠€涔堝悕瀛楋紵'" -ForegroundColor White
Write-Host "`n3. Check progress.txt for detailed logs" -ForegroundColor Cyan

Write-Progress "Comprehensive test completed" "Success" "All phases executed, check individual results above"

Write-Host "`n=== Test Completed ===" -ForegroundColor Green
Write-Host "Check progress.txt for detailed execution log" -ForegroundColor Yellow