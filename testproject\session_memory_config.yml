version: 1

agent:
  # Additional system prompt to append to the agent's default system prompt
  additionalSystemPrompt: null
  # Enable streaming responses from the AI model
  streaming: true
  # Temperature setting for AI model responses (0.0-1.0)
  temperature: 0.3
  experimental:
    # Enable/disable the agent to run in shadow mode. This will run the agent on
    # a temporary clone of your workspace, prompting you before any changes are
    # applied to your working directory.
    enableShadowMode: false

sessions:
  # Automatically restore the last active session on startup
  autoRestore: false
  # Directory where session data is stored
  persistenceDir: C:\Users\<USER>\.rovodev\sessions

console:
  # Output format for console display (markdown, simple, or raw)
  outputFormat: markdown
  # Show tool execution results in the console
  showToolResults: true

logging:
  # Path to the log file
  path: C:\Users\<USER>\.rovodev\rovodev.log

mcp:
  # Path to the MCP (Model Context Protocol) configuration file
  mcpConfigPath: C:\Users\<USER>\.rovodev\mcp.json

toolPermissions:
  allowAll: false
  # Default permission for tools not explicitly listed
  default: ask
  # Permission settings for specific tools
  tools:
    create_file: allow
    delete_file: allow
    find_and_replace_code: allow
    open_files: allow
    expand_code_chunks: allow
    expand_folder: allow
    grep_file_content: allow
    grep_file_paths: allow
    getAccessibleAtlassianResources: allow
    getConfluenceSpaces: allow
    getConfluencePages: allow
    getPagesInConfluenceSpace: allow
    getConfluencePageAncestors: allow
    getConfluencePageFooterComments: allow
    getConfluencePageInlineComments: allow
    getConfluencePageDescendants: allow
    searchConfluenceUsingCql: allow
    getJiraIssue: allow
    getTransitionsForJiraIssue: allow
    lookupJiraAccountId: allow
    searchJiraIssuesUsingJql: allow
    getJiraIssueRemoteIssueLinks: allow
    getVisibleJiraProjects: allow
    getJiraProjectIssueTypesMetadata: allow
    createConfluencePage: ask
    updateConfluencePage: ask
    createConfluenceFooterComment: ask
    createConfluenceInlineComment: ask
    editJiraIssue: ask
    createJiraIssue: ask
    transitionJiraIssue: ask
    addCommentToJiraIssue: ask
    create_technical_plan: allow
  bash:
    # Default permission for bash commands not explicitly listed
    default: ask
    # List of specific bash commands with their permission settings
    commands:
    - command: ls.*
      permission: allow
    - command: cat.*
      permission: allow
    - command: echo.*
      permission: allow
    - command: git status
      permission: allow
    - command: git diff.*
      permission: allow
    - command: git log.*
      permission: allow
    - command: pwd
      permission: allow
    - command: Get-ChildItem -Force | Format-Table Name, Length, LastWriteTime
      permission: allow
    - command: Test-Path "chat_sessions" -PathType Container
      permission: allow
    - command: Test-Path "logs" -PathType Container
      permission: allow
    - command: dir
      permission: allow
    - command: Test-Path "C:\prg2\acli.exe"
      permission: allow
    - command: Get-Content "config.ini" | Select-String "config_file"
      permission: allow
    - command: .\test_session_memory.ps1
      permission: allow
    - command: .\tmp_rovodev_simple_test.ps1
      permission: allow
    - command: ls
      permission: allow
    - command: Get-Content test_session_memory.ps1 -Encoding UTF8
      permission: allow
  # List of allowed MCP server names
  allowedMcpServers: []
