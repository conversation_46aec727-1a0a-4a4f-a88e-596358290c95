[2025-07-16 15:42:23] === GUI 程序启动 ===
[2025-07-16 15:42:23] 开始创建窗体
[2025-07-16 15:42:23] 设置窗体属性
[2025-07-16 15:42:23] 创建 GUI 控件
[2025-07-16 15:42:23] 开始创建 GUI 控件
[2025-07-16 15:42:23] 创建聊天显示区
[2025-07-16 15:42:23] GUI 控件创建完成
[2025-07-16 15:42:23] 创建初始化定时器
[2025-07-16 15:42:23] 初始化变量
[2025-07-16 15:42:23] 开始初始化会话
[2025-07-16 15:42:23] 开始初始化会话
[2025-07-16 15:42:23] 设置配置文件路径
[2025-07-16 15:42:23] 检查配置文件: fresh_session_config.yml
[2025-07-16 15:42:23] 检查 acli.exe
[2025-07-16 15:42:23] 找到 acli.exe 在上级目录
[2025-07-16 15:42:23] 读取配置文件中的会话目录设置
[2025-07-16 15:42:23] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 15:42:23] 未找到任何会话目录
[2025-07-16 15:42:23] 会话初始化失败
[2025-07-16 15:42:23] 启动自动认证检查定时器
[2025-07-16 15:42:23] === GUI 程序启动完成 ===
[2025-07-16 15:42:23] === 窗体显示事件触发 ===
[2025-07-16 15:42:23] === 开始自动认证检查 ===
[2025-07-16 15:42:23] 调用 CheckAuthStatus
[2025-07-16 15:42:23] === 开始检查认证状态 ===
[2025-07-16 15:42:23] 设置 Process 参数
[2025-07-16 15:42:23] 设置 Process 选项
[2025-07-16 15:42:23] 开始执行认证状态检查
[2025-07-16 15:42:25] 认证状态检查完成，退出代码: 0
[2025-07-16 15:42:25] 获取到输出，行数: 1
[2025-07-16 15:42:25] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:25] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:25] 用户邮箱: 
[2025-07-16 15:42:25] 认证状态: 已认证
[2025-07-16 15:42:25] 是否已认证: True
[2025-07-16 15:42:25] === 更新窗体标题 ===
[2025-07-16 15:42:25] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:42:25] === 认证状态检查完成 ===
[2025-07-16 15:42:25] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:25] === 定时器触发，开始自动认证检查 ===
[2025-07-16 15:42:25] === 开始自动认证检查 ===
[2025-07-16 15:42:25] 调用 CheckAuthStatus
[2025-07-16 15:42:25] === 开始检查认证状态 ===
[2025-07-16 15:42:25] 设置 Process 参数
[2025-07-16 15:42:25] 设置 Process 选项
[2025-07-16 15:42:25] 开始执行认证状态检查
[2025-07-16 15:42:27] 认证状态检查完成，退出代码: 0
[2025-07-16 15:42:27] 获取到输出，行数: 1
[2025-07-16 15:42:27] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:27] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:27] 用户邮箱: 
[2025-07-16 15:42:27] 认证状态: 已认证
[2025-07-16 15:42:27] 是否已认证: True
[2025-07-16 15:42:27] === 更新窗体标题 ===
[2025-07-16 15:42:27] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:42:27] === 认证状态检查完成 ===
[2025-07-16 15:42:27] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:42:27] 认证状态：已认证
[2025-07-16 15:42:27] === 自动认证检查完成 ===
[2025-07-16 15:42:27] 认证状态：已认证
[2025-07-16 15:42:27] === 自动认证检查完成 ===
[2025-07-16 15:42:38] === 开始发送问题 ===
[2025-07-16 15:42:38] 用户问题: 你好
[2025-07-16 15:42:38] 禁用按钮和输入框
[2025-07-16 15:42:38] 聊天计数: 1
[2025-07-16 15:42:38] 开始调用 SendToAI
[2025-07-16 15:42:38] 进入 SendToAI 方法，问题: 你好
[2025-07-16 15:42:38] 显示发送状态
[2025-07-16 15:42:38] 创建 TProcess
[2025-07-16 15:42:38] 设置 Process 参数
[2025-07-16 15:42:38] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 15:42:38] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 15:42:38] 设置 Process 选项
[2025-07-16 15:42:38] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 15:42:38] 开始执行 Process (流式模式)
[2025-07-16 15:42:38] Process 启动成功，开始流式处理...
[2025-07-16 15:42:38] 流式进程已启动，进程ID: 10016
[2025-07-16 15:42:38] 开始等待会话文件更新...
[2025-07-16 15:42:38] 会话文件不存在，等待创建
[2025-07-16 15:42:43] 等待会话文件更新... 5秒
[2025-07-16 15:42:49] 等待会话文件更新... 10秒
[2025-07-16 15:42:54] 等待会话文件更新... 15秒
[2025-07-16 15:43:00] 等待会话文件更新... 20秒
[2025-07-16 15:43:05] 等待会话文件更新... 25秒
[2025-07-16 15:43:11] 等待会话文件更新... 30秒
[2025-07-16 15:43:11] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 15:43:11] SendToAI 返回，回复长度: 0
[2025-07-16 15:47:49] === GUI 程序启动 ===
[2025-07-16 15:47:49] 开始创建窗体
[2025-07-16 15:47:49] 设置窗体属性
[2025-07-16 15:47:49] 创建 GUI 控件
[2025-07-16 15:47:49] 开始创建 GUI 控件
[2025-07-16 15:47:49] 创建聊天显示区
[2025-07-16 15:47:49] GUI 控件创建完成
[2025-07-16 15:47:49] 创建初始化定时器
[2025-07-16 15:47:49] 初始化变量
[2025-07-16 15:47:49] 开始初始化会话
[2025-07-16 15:47:49] 开始初始化会话
[2025-07-16 15:47:49] 设置配置文件路径
[2025-07-16 15:47:49] 检查配置文件: fresh_session_config.yml
[2025-07-16 15:47:49] 检查 acli.exe
[2025-07-16 15:47:49] 找到 acli.exe 在上级目录
[2025-07-16 15:47:49] 读取配置文件中的会话目录设置
[2025-07-16 15:47:49] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 15:47:49] 未找到任何会话目录
[2025-07-16 15:47:49] 会话初始化失败
[2025-07-16 15:47:49] 启动自动认证检查定时器
[2025-07-16 15:47:49] === GUI 程序启动完成 ===
[2025-07-16 15:47:49] === 窗体显示事件触发 ===
[2025-07-16 15:47:49] === 开始自动认证检查 ===
[2025-07-16 15:47:49] 调用 CheckAuthStatus
[2025-07-16 15:47:49] === 开始检查认证状态 ===
[2025-07-16 15:47:49] 设置 Process 参数
[2025-07-16 15:47:49] 设置 Process 选项
[2025-07-16 15:47:49] 开始执行认证状态检查
[2025-07-16 15:47:51] 认证状态检查完成，退出代码: 0
[2025-07-16 15:47:51] 获取到输出，行数: 1
[2025-07-16 15:47:51] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:51] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:51] 用户邮箱: 
[2025-07-16 15:47:51] 认证状态: 已认证
[2025-07-16 15:47:51] 是否已认证: True
[2025-07-16 15:47:51] === 更新窗体标题 ===
[2025-07-16 15:47:51] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:47:51] === 认证状态检查完成 ===
[2025-07-16 15:47:51] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:51] === 定时器触发，开始自动认证检查 ===
[2025-07-16 15:47:51] === 开始自动认证检查 ===
[2025-07-16 15:47:51] 调用 CheckAuthStatus
[2025-07-16 15:47:51] === 开始检查认证状态 ===
[2025-07-16 15:47:51] 设置 Process 参数
[2025-07-16 15:47:51] 设置 Process 选项
[2025-07-16 15:47:51] 开始执行认证状态检查
[2025-07-16 15:47:53] 认证状态检查完成，退出代码: 0
[2025-07-16 15:47:53] 获取到输出，行数: 1
[2025-07-16 15:47:53] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:53] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:53] 用户邮箱: 
[2025-07-16 15:47:53] 认证状态: 已认证
[2025-07-16 15:47:53] 是否已认证: True
[2025-07-16 15:47:53] === 更新窗体标题 ===
[2025-07-16 15:47:53] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:47:53] === 认证状态检查完成 ===
[2025-07-16 15:47:53] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:47:53] 认证状态：已认证
[2025-07-16 15:47:53] === 自动认证检查完成 ===
[2025-07-16 15:47:53] 认证状态：已认证
[2025-07-16 15:47:53] === 自动认证检查完成 ===
[2025-07-16 15:48:02] === 开始发送问题 ===
[2025-07-16 15:48:02] 用户问题: 你好
[2025-07-16 15:48:02] 禁用按钮和输入框
[2025-07-16 15:48:02] 聊天计数: 1
[2025-07-16 15:48:02] 开始调用 SendToAI
[2025-07-16 15:48:02] 进入 SendToAI 方法，问题: 你好
[2025-07-16 15:48:02] 显示发送状态
[2025-07-16 15:48:02] 创建 TProcess
[2025-07-16 15:48:02] 设置 Process 参数
[2025-07-16 15:48:02] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 15:48:02] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 15:48:02] 设置 Process 选项
[2025-07-16 15:48:02] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 15:48:02] 开始执行 Process (流式模式)
[2025-07-16 15:48:02] Process 启动成功，开始流式处理...
[2025-07-16 15:48:02] 流式进程已启动，进程ID: 5668
[2025-07-16 15:48:02] 开始等待会话文件更新...
[2025-07-16 15:48:02] 会话文件不存在，等待创建
[2025-07-16 15:48:07] 等待会话文件更新... 5秒
[2025-07-16 15:48:13] 等待会话文件更新... 10秒
[2025-07-16 15:48:18] 等待会话文件更新... 15秒
[2025-07-16 15:48:24] 等待会话文件更新... 20秒
[2025-07-16 15:48:29] 等待会话文件更新... 25秒
[2025-07-16 15:48:35] 等待会话文件更新... 30秒
[2025-07-16 15:48:35] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 15:48:35] SendToAI 返回，回复长度: 0
[2025-07-16 15:51:19] === GUI 程序启动 ===
[2025-07-16 15:51:19] 开始创建窗体
[2025-07-16 15:51:19] 设置窗体属性
[2025-07-16 15:51:19] 创建 GUI 控件
[2025-07-16 15:51:19] 开始创建 GUI 控件
[2025-07-16 15:51:19] 创建聊天显示区
[2025-07-16 15:51:19] GUI 控件创建完成
[2025-07-16 15:51:19] 创建初始化定时器
[2025-07-16 15:51:19] 初始化变量
[2025-07-16 15:51:19] 开始初始化会话
[2025-07-16 15:51:19] 开始初始化会话
[2025-07-16 15:51:19] 设置配置文件路径
[2025-07-16 15:51:19] 检查配置文件: fresh_session_config.yml
[2025-07-16 15:51:19] 检查 acli.exe
[2025-07-16 15:51:19] 找到 acli.exe 在上级目录
[2025-07-16 15:51:19] 读取配置文件中的会话目录设置
[2025-07-16 15:51:19] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 15:51:19] 未找到任何会话目录
[2025-07-16 15:51:19] 会话初始化失败
[2025-07-16 15:51:19] 启动自动认证检查定时器
[2025-07-16 15:51:19] === GUI 程序启动完成 ===
[2025-07-16 15:51:19] === 窗体显示事件触发 ===
[2025-07-16 15:51:19] === 开始自动认证检查 ===
[2025-07-16 15:51:19] 调用 CheckAuthStatus
[2025-07-16 15:51:19] === 开始检查认证状态 ===
[2025-07-16 15:51:19] 设置 Process 参数
[2025-07-16 15:51:19] 设置 Process 选项
[2025-07-16 15:51:19] 开始执行认证状态检查
[2025-07-16 15:51:21] 认证状态检查完成，退出代码: 0
[2025-07-16 15:51:21] 获取到输出，行数: 1
[2025-07-16 15:51:21] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:21] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:21] 用户邮箱: 
[2025-07-16 15:51:21] 认证状态: 已认证
[2025-07-16 15:51:21] 是否已认证: True
[2025-07-16 15:51:21] === 更新窗体标题 ===
[2025-07-16 15:51:21] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:51:21] === 认证状态检查完成 ===
[2025-07-16 15:51:21] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:21] === 定时器触发，开始自动认证检查 ===
[2025-07-16 15:51:21] === 开始自动认证检查 ===
[2025-07-16 15:51:21] 调用 CheckAuthStatus
[2025-07-16 15:51:21] === 开始检查认证状态 ===
[2025-07-16 15:51:21] 设置 Process 参数
[2025-07-16 15:51:21] 设置 Process 选项
[2025-07-16 15:51:21] 开始执行认证状态检查
[2025-07-16 15:51:23] 认证状态检查完成，退出代码: 0
[2025-07-16 15:51:23] 获取到输出，行数: 1
[2025-07-16 15:51:23] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:23] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:23] 用户邮箱: 
[2025-07-16 15:51:23] 认证状态: 已认证
[2025-07-16 15:51:23] 是否已认证: True
[2025-07-16 15:51:23] === 更新窗体标题 ===
[2025-07-16 15:51:23] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:51:23] === 认证状态检查完成 ===
[2025-07-16 15:51:23] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:51:23] 认证状态：已认证
[2025-07-16 15:51:23] === 自动认证检查完成 ===
[2025-07-16 15:51:23] 认证状态：已认证
[2025-07-16 15:51:23] === 自动认证检查完成 ===
[2025-07-16 15:54:57] === GUI 程序启动 ===
[2025-07-16 15:54:57] 开始创建窗体
[2025-07-16 15:54:57] 设置窗体属性
[2025-07-16 15:54:57] 创建 GUI 控件
[2025-07-16 15:54:57] 开始创建 GUI 控件
[2025-07-16 15:54:57] 创建聊天显示区
[2025-07-16 15:54:57] GUI 控件创建完成
[2025-07-16 15:54:57] 创建初始化定时器
[2025-07-16 15:54:57] 初始化变量
[2025-07-16 15:54:57] 开始初始化会话
[2025-07-16 15:54:57] 开始初始化会话
[2025-07-16 15:54:57] 设置配置文件路径
[2025-07-16 15:54:57] 检查配置文件: fresh_session_config.yml
[2025-07-16 15:54:57] 检查 acli.exe
[2025-07-16 15:54:57] 找到 acli.exe 在上级目录
[2025-07-16 15:54:57] 读取配置文件中的会话目录设置
[2025-07-16 15:54:57] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 15:54:57] 未找到任何会话目录
[2025-07-16 15:54:57] 会话初始化失败
[2025-07-16 15:54:57] 启动自动认证检查定时器
[2025-07-16 15:54:57] === GUI 程序启动完成 ===
[2025-07-16 15:54:57] === 窗体显示事件触发 ===
[2025-07-16 15:54:57] === 开始自动认证检查 ===
[2025-07-16 15:54:57] 调用 CheckAuthStatus
[2025-07-16 15:54:57] === 开始检查认证状态 ===
[2025-07-16 15:54:57] 设置 Process 参数
[2025-07-16 15:54:57] 设置 Process 选项
[2025-07-16 15:54:57] 开始执行认证状态检查
[2025-07-16 15:54:59] 认证状态检查完成，退出代码: 0
[2025-07-16 15:54:59] 获取到输出，行数: 1
[2025-07-16 15:54:59] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:54:59] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:54:59] 用户邮箱: 
[2025-07-16 15:54:59] 认证状态: 已认证
[2025-07-16 15:54:59] 是否已认证: True
[2025-07-16 15:54:59] === 更新窗体标题 ===
[2025-07-16 15:54:59] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:54:59] === 认证状态检查完成 ===
[2025-07-16 15:54:59] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:54:59] === 定时器触发，开始自动认证检查 ===
[2025-07-16 15:54:59] === 开始自动认证检查 ===
[2025-07-16 15:54:59] 调用 CheckAuthStatus
[2025-07-16 15:54:59] === 开始检查认证状态 ===
[2025-07-16 15:54:59] 设置 Process 参数
[2025-07-16 15:54:59] 设置 Process 选项
[2025-07-16 15:54:59] 开始执行认证状态检查
[2025-07-16 15:55:02] 认证状态检查完成，退出代码: 0
[2025-07-16 15:55:02] 获取到输出，行数: 1
[2025-07-16 15:55:02] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:55:02] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:55:02] 用户邮箱: 
[2025-07-16 15:55:02] 认证状态: 已认证
[2025-07-16 15:55:02] 是否已认证: True
[2025-07-16 15:55:02] === 更新窗体标题 ===
[2025-07-16 15:55:02] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 15:55:02] === 认证状态检查完成 ===
[2025-07-16 15:55:02] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 15:55:02] 认证状态：已认证
[2025-07-16 15:55:02] === 自动认证检查完成 ===
[2025-07-16 15:55:02] 认证状态：已认证
[2025-07-16 15:55:02] === 自动认证检查完成 ===
[2025-07-16 15:55:09] === 开始发送问题 ===
[2025-07-16 15:55:09] 用户问题: 你好
[2025-07-16 15:55:09] 禁用按钮和输入框
[2025-07-16 15:55:09] 聊天计数: 1
[2025-07-16 15:55:09] 开始调用 SendToAI
[2025-07-16 15:55:09] 进入 SendToAI 方法，问题: 你好
[2025-07-16 15:55:09] 显示发送状态
[2025-07-16 15:55:09] 创建 TProcess
[2025-07-16 15:55:09] 设置 Process 参数
[2025-07-16 15:55:09] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 15:55:09] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 15:55:09] 准备通过标准输入发送问题: 你好
[2025-07-16 15:55:09] 设置 Process 选项
[2025-07-16 15:55:09] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 15:55:09] 准备执行命令:
[2025-07-16 15:55:09]   可执行文件: ..\acli.exe
[2025-07-16 15:55:09]   工作目录: C:\test\chatsessiontest
[2025-07-16 15:55:09]   参数列表:
[2025-07-16 15:55:09]     [0] rovodev
[2025-07-16 15:55:09]     [1] run
[2025-07-16 15:55:09]     [2] --config-file
[2025-07-16 15:55:09]     [3] C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 15:55:09] 完整命令: ..\acli.exe "rovodev" "run" "--config-file" "C:\test\chatsessiontest\fresh_session_config.yml"
[2025-07-16 15:55:09] 开始执行 Process (流式模式)
[2025-07-16 15:55:09] Process 启动成功，开始流式处理...
[2025-07-16 15:55:09] 流式进程已启动，进程ID: 11680
[2025-07-16 15:55:09] 流式进程已启动，进程ID: 11680
[2025-07-16 15:55:09] 开始等待会话文件更新...
[2025-07-16 15:55:09] 会话文件不存在，等待创建
[2025-07-16 15:55:15] 等待会话文件更新... 5秒
[2025-07-16 15:55:20] 等待会话文件更新... 10秒
[2025-07-16 15:55:26] 等待会话文件更新... 15秒
[2025-07-16 15:55:31] 等待会话文件更新... 20秒
[2025-07-16 15:55:37] 等待会话文件更新... 25秒
[2025-07-16 15:55:42] 等待会话文件更新... 30秒
[2025-07-16 15:55:42] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 15:55:42] SendToAI 返回，回复长度: 0
[2025-07-16 16:01:17] === GUI 程序启动 ===
[2025-07-16 16:01:17] 开始创建窗体
[2025-07-16 16:01:17] 设置窗体属性
[2025-07-16 16:01:17] 创建 GUI 控件
[2025-07-16 16:01:17] 开始创建 GUI 控件
[2025-07-16 16:01:17] 创建聊天显示区
[2025-07-16 16:01:17] GUI 控件创建完成
[2025-07-16 16:01:17] 创建初始化定时器
[2025-07-16 16:01:17] 初始化变量
[2025-07-16 16:01:17] 开始初始化会话
[2025-07-16 16:01:17] 开始初始化会话
[2025-07-16 16:01:17] 设置配置文件路径
[2025-07-16 16:01:17] 检查配置文件: fresh_session_config.yml
[2025-07-16 16:01:17] 检查 acli.exe
[2025-07-16 16:01:17] 找到 acli.exe 在上级目录
[2025-07-16 16:01:17] 读取配置文件中的会话目录设置
[2025-07-16 16:01:17] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 16:01:17] 未找到任何会话目录
[2025-07-16 16:01:17] 会话初始化失败
[2025-07-16 16:01:17] 启动自动认证检查定时器
[2025-07-16 16:01:17] === GUI 程序启动完成 ===
[2025-07-16 16:01:17] === 窗体显示事件触发 ===
[2025-07-16 16:01:17] === 开始自动认证检查 ===
[2025-07-16 16:01:17] 调用 CheckAuthStatus
[2025-07-16 16:01:17] === 开始检查认证状态 ===
[2025-07-16 16:01:17] 设置 Process 参数
[2025-07-16 16:01:17] 设置 Process 选项
[2025-07-16 16:01:17] 开始执行认证状态检查
[2025-07-16 16:01:19] 认证状态检查完成，退出代码: 0
[2025-07-16 16:01:19] 获取到输出，行数: 1
[2025-07-16 16:01:19] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:19] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:19] 用户邮箱: 
[2025-07-16 16:01:19] 认证状态: 已认证
[2025-07-16 16:01:19] 是否已认证: True
[2025-07-16 16:01:19] === 更新窗体标题 ===
[2025-07-16 16:01:19] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 16:01:19] === 认证状态检查完成 ===
[2025-07-16 16:01:19] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:19] === 定时器触发，开始自动认证检查 ===
[2025-07-16 16:01:19] === 开始自动认证检查 ===
[2025-07-16 16:01:19] 调用 CheckAuthStatus
[2025-07-16 16:01:19] === 开始检查认证状态 ===
[2025-07-16 16:01:19] 设置 Process 参数
[2025-07-16 16:01:19] 设置 Process 选项
[2025-07-16 16:01:19] 开始执行认证状态检查
[2025-07-16 16:01:21] 认证状态检查完成，退出代码: 0
[2025-07-16 16:01:21] 获取到输出，行数: 1
[2025-07-16 16:01:21] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:21] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:21] 用户邮箱: 
[2025-07-16 16:01:21] 认证状态: 已认证
[2025-07-16 16:01:21] 是否已认证: True
[2025-07-16 16:01:21] === 更新窗体标题 ===
[2025-07-16 16:01:21] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 16:01:21] === 认证状态检查完成 ===
[2025-07-16 16:01:21] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 16:01:21] 认证状态：已认证
[2025-07-16 16:01:21] === 自动认证检查完成 ===
[2025-07-16 16:01:21] 认证状态：已认证
[2025-07-16 16:01:21] === 自动认证检查完成 ===
[2025-07-16 16:01:57] === 开始发送问题 ===
[2025-07-16 16:01:57] 用户问题: 你好
[2025-07-16 16:01:57] 禁用按钮和输入框
[2025-07-16 16:01:57] 聊天计数: 1
[2025-07-16 16:01:57] 开始调用 SendToAI
[2025-07-16 16:01:57] 进入 SendToAI 方法，问题: 你好
[2025-07-16 16:01:57] 显示发送状态
[2025-07-16 16:01:57] 创建 TProcess
[2025-07-16 16:01:57] 设置 Process 参数
[2025-07-16 16:01:57] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 16:01:57] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 16:01:57] 添加用户问题参数: 你好
[2025-07-16 16:01:57] 设置 Process 选项
[2025-07-16 16:01:57] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 16:01:57] 准备执行命令:
[2025-07-16 16:01:57]   可执行文件: ..\acli.exe
[2025-07-16 16:01:57]   工作目录: C:\test\chatsessiontest
[2025-07-16 16:01:57]   参数列表:
[2025-07-16 16:01:57]     [0] rovodev
[2025-07-16 16:01:57]     [1] run
[2025-07-16 16:01:57]     [2] --config-file
[2025-07-16 16:01:57]     [3] C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 16:01:57]     [4] 你好
[2025-07-16 16:01:57] 完整命令: ..\acli.exe "rovodev" "run" "--config-file" "C:\test\chatsessiontest\fresh_session_config.yml" "你好"
[2025-07-16 16:01:57] 开始执行 Process (流式模式)
[2025-07-16 16:01:57] Process 启动成功，开始流式处理...
[2025-07-16 16:01:57] 流式进程已启动，进程ID: 14032
[2025-07-16 16:01:57] 流式进程已启动，进程ID: 14032
[2025-07-16 16:01:57] 开始等待会话文件更新...
[2025-07-16 16:01:57] 会话文件不存在，等待创建
[2025-07-16 16:02:03] 等待会话文件更新... 5秒
[2025-07-16 16:02:08] 等待会话文件更新... 10秒
[2025-07-16 16:02:14] 等待会话文件更新... 15秒
[2025-07-16 16:02:19] 等待会话文件更新... 20秒
[2025-07-16 16:02:25] 等待会话文件更新... 25秒
[2025-07-16 16:02:31] 等待会话文件更新... 30秒
[2025-07-16 16:02:31] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 16:02:31] SendToAI 返回，回复长度: 0
[2025-07-16 20:03:43] === GUI 程序启动 ===
[2025-07-16 20:03:43] 开始创建窗体
[2025-07-16 20:03:43] 设置窗体属性
[2025-07-16 20:03:43] 创建 GUI 控件
[2025-07-16 20:03:43] 开始创建 GUI 控件
[2025-07-16 20:03:43] 创建聊天显示区
[2025-07-16 20:03:43] GUI 控件创建完成
[2025-07-16 20:03:43] 创建初始化定时器
[2025-07-16 20:03:43] 初始化变量
[2025-07-16 20:03:43] 开始初始化会话
[2025-07-16 20:03:43] 开始初始化会话
[2025-07-16 20:03:43] 设置配置文件路径
[2025-07-16 20:03:43] 检查配置文件: fresh_session_config.yml
[2025-07-16 20:03:43] 检查 acli.exe
[2025-07-16 20:03:43] 找到 acli.exe 在上级目录
[2025-07-16 20:03:43] 读取配置文件中的会话目录设置
[2025-07-16 20:03:43] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 20:03:43] 未找到任何会话目录
[2025-07-16 20:03:43] 会话初始化失败
[2025-07-16 20:03:43] 启动自动认证检查定时器
[2025-07-16 20:03:43] === GUI 程序启动完成 ===
[2025-07-16 20:03:43] === 窗体显示事件触发 ===
[2025-07-16 20:03:43] === 开始自动认证检查 ===
[2025-07-16 20:03:43] 调用 CheckAuthStatus
[2025-07-16 20:03:43] === 开始检查认证状态 ===
[2025-07-16 20:03:43] 设置 Process 参数
[2025-07-16 20:03:43] 设置 Process 选项
[2025-07-16 20:03:43] 开始执行认证状态检查
[2025-07-16 20:03:45] 认证状态检查完成，退出代码: 0
[2025-07-16 20:03:45] 获取到输出，行数: 1
[2025-07-16 20:03:45] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:45] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:45] 用户邮箱: 
[2025-07-16 20:03:45] 认证状态: 已认证
[2025-07-16 20:03:45] 是否已认证: True
[2025-07-16 20:03:45] === 更新窗体标题 ===
[2025-07-16 20:03:45] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:03:45] === 认证状态检查完成 ===
[2025-07-16 20:03:45] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:45] === 定时器触发，开始自动认证检查 ===
[2025-07-16 20:03:45] === 开始自动认证检查 ===
[2025-07-16 20:03:45] 调用 CheckAuthStatus
[2025-07-16 20:03:45] === 开始检查认证状态 ===
[2025-07-16 20:03:45] 设置 Process 参数
[2025-07-16 20:03:45] 设置 Process 选项
[2025-07-16 20:03:45] 开始执行认证状态检查
[2025-07-16 20:03:47] 认证状态检查完成，退出代码: 0
[2025-07-16 20:03:47] 获取到输出，行数: 1
[2025-07-16 20:03:47] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:47] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:47] 用户邮箱: 
[2025-07-16 20:03:47] 认证状态: 已认证
[2025-07-16 20:03:47] 是否已认证: True
[2025-07-16 20:03:47] === 更新窗体标题 ===
[2025-07-16 20:03:47] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:03:47] === 认证状态检查完成 ===
[2025-07-16 20:03:47] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:03:47] 认证状态：已认证
[2025-07-16 20:03:47] === 自动认证检查完成 ===
[2025-07-16 20:03:47] 认证状态：已认证
[2025-07-16 20:03:47] === 自动认证检查完成 ===
[2025-07-16 20:04:01] === 开始发送问题 ===
[2025-07-16 20:04:01] 用户问题: 你好
[2025-07-16 20:04:01] 禁用按钮和输入框
[2025-07-16 20:04:01] 聊天计数: 1
[2025-07-16 20:04:01] 开始调用 SendToAI
[2025-07-16 20:04:01] 进入 SendToAI 方法，问题: 你好
[2025-07-16 20:04:01] 显示发送状态
[2025-07-16 20:04:01] 创建 TProcess
[2025-07-16 20:04:01] 设置 Process 参数
[2025-07-16 20:04:01] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 20:04:01] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:04:01] 添加用户问题参数: 你好
[2025-07-16 20:04:01] 设置 Process 选项
[2025-07-16 20:04:01] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 20:04:01] 准备执行命令:
[2025-07-16 20:04:01]   可执行文件: ..\acli.exe
[2025-07-16 20:04:01]   工作目录: C:\test\chatsessiontest
[2025-07-16 20:04:01]   参数列表:
[2025-07-16 20:04:01]     [0] rovodev
[2025-07-16 20:04:01]     [1] run
[2025-07-16 20:04:01]     [2] --config-file
[2025-07-16 20:04:01]     [3] C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:04:01]     [4] 你好
[2025-07-16 20:04:01] 完整命令: ..\acli.exe "rovodev" "run" "--config-file" "C:\test\chatsessiontest\fresh_session_config.yml" "你好"
[2025-07-16 20:04:01] 开始执行 Process (流式模式)
[2025-07-16 20:04:01] Process 启动成功，开始流式处理...
[2025-07-16 20:04:01] 流式进程已启动，进程ID: 1636
[2025-07-16 20:04:01] 流式进程已启动，进程ID: 1636
[2025-07-16 20:04:01] 开始等待会话文件更新...
[2025-07-16 20:04:01] 会话文件不存在，等待创建
[2025-07-16 20:04:07] 等待会话文件更新... 5秒
[2025-07-16 20:04:12] 等待会话文件更新... 10秒
[2025-07-16 20:04:18] 等待会话文件更新... 15秒
[2025-07-16 20:04:23] 等待会话文件更新... 20秒
[2025-07-16 20:04:29] 等待会话文件更新... 25秒
[2025-07-16 20:04:34] 等待会话文件更新... 30秒
[2025-07-16 20:04:34] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 20:04:34] SendToAI 返回，回复长度: 0
[2025-07-16 20:06:19] === GUI 程序启动 ===
[2025-07-16 20:06:19] 开始创建窗体
[2025-07-16 20:06:19] 设置窗体属性
[2025-07-16 20:06:19] 创建 GUI 控件
[2025-07-16 20:06:19] 开始创建 GUI 控件
[2025-07-16 20:06:19] 创建聊天显示区
[2025-07-16 20:06:19] GUI 控件创建完成
[2025-07-16 20:06:19] 创建初始化定时器
[2025-07-16 20:06:19] 初始化变量
[2025-07-16 20:06:19] 开始初始化会话
[2025-07-16 20:06:19] 开始初始化会话
[2025-07-16 20:06:19] 设置配置文件路径
[2025-07-16 20:06:19] 检查配置文件: fresh_session_config.yml
[2025-07-16 20:06:19] 检查 acli.exe
[2025-07-16 20:06:19] 找到 acli.exe 在上级目录
[2025-07-16 20:06:19] 读取配置文件中的会话目录设置
[2025-07-16 20:06:19] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 20:06:19] 未找到任何会话目录
[2025-07-16 20:06:19] 会话初始化失败
[2025-07-16 20:06:19] 启动自动认证检查定时器
[2025-07-16 20:06:19] === GUI 程序启动完成 ===
[2025-07-16 20:06:19] === 窗体显示事件触发 ===
[2025-07-16 20:06:19] === 开始自动认证检查 ===
[2025-07-16 20:06:19] 调用 CheckAuthStatus
[2025-07-16 20:06:19] === 开始检查认证状态 ===
[2025-07-16 20:06:19] 设置 Process 参数
[2025-07-16 20:06:19] 设置 Process 选项
[2025-07-16 20:06:19] 开始执行认证状态检查
[2025-07-16 20:06:22] 认证状态检查完成，退出代码: 0
[2025-07-16 20:06:22] 获取到输出，行数: 1
[2025-07-16 20:06:22] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:22] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:22] 用户邮箱: 
[2025-07-16 20:06:22] 认证状态: 已认证
[2025-07-16 20:06:22] 是否已认证: True
[2025-07-16 20:06:22] === 更新窗体标题 ===
[2025-07-16 20:06:22] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:06:22] === 认证状态检查完成 ===
[2025-07-16 20:06:22] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:22] === 定时器触发，开始自动认证检查 ===
[2025-07-16 20:06:22] === 开始自动认证检查 ===
[2025-07-16 20:06:22] 调用 CheckAuthStatus
[2025-07-16 20:06:22] === 开始检查认证状态 ===
[2025-07-16 20:06:22] 设置 Process 参数
[2025-07-16 20:06:22] 设置 Process 选项
[2025-07-16 20:06:22] 开始执行认证状态检查
[2025-07-16 20:06:26] 认证状态检查完成，退出代码: 0
[2025-07-16 20:06:26] 获取到输出，行数: 1
[2025-07-16 20:06:26] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:26] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:26] 用户邮箱: 
[2025-07-16 20:06:26] 认证状态: 已认证
[2025-07-16 20:06:26] 是否已认证: True
[2025-07-16 20:06:26] === 更新窗体标题 ===
[2025-07-16 20:06:26] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:06:26] === 认证状态检查完成 ===
[2025-07-16 20:06:26] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:06:26] 认证状态：已认证
[2025-07-16 20:06:26] === 自动认证检查完成 ===
[2025-07-16 20:06:26] 认证状态：已认证
[2025-07-16 20:06:26] === 自动认证检查完成 ===
[2025-07-16 20:06:34] === 开始发送问题 ===
[2025-07-16 20:06:34] 用户问题: 测试
[2025-07-16 20:06:34] 禁用按钮和输入框
[2025-07-16 20:06:34] 聊天计数: 1
[2025-07-16 20:06:34] 开始调用 SendToAI
[2025-07-16 20:06:34] 进入 SendToAI 方法，问题: 测试
[2025-07-16 20:06:34] 显示发送状态
[2025-07-16 20:06:34] 创建 TProcess
[2025-07-16 20:06:34] 设置 Process 参数
[2025-07-16 20:06:34] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 20:06:34] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:06:34] 添加用户问题参数: 测试
[2025-07-16 20:06:34] 设置 Process 选项
[2025-07-16 20:06:34] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 20:06:34] 准备执行命令:
[2025-07-16 20:06:34]   可执行文件: ..\acli.exe
[2025-07-16 20:06:34]   工作目录: C:\test\chatsessiontest
[2025-07-16 20:06:34]   参数列表:
[2025-07-16 20:06:34]     [0] rovodev
[2025-07-16 20:06:34]     [1] run
[2025-07-16 20:06:34]     [2] --config-file
[2025-07-16 20:06:34]     [3] C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:06:34]     [4] 测试
[2025-07-16 20:06:34] 完整命令: ..\acli.exe "rovodev" "run" "--config-file" "C:\test\chatsessiontest\fresh_session_config.yml" "测试"
[2025-07-16 20:06:34] 开始执行 Process (流式模式)
[2025-07-16 20:06:34] Process 启动成功，开始流式处理...
[2025-07-16 20:06:34] 流式进程已启动，进程ID: 8184
[2025-07-16 20:06:34] 流式进程已启动，进程ID: 8184
[2025-07-16 20:06:34] 开始等待会话文件更新...
[2025-07-16 20:06:34] 会话文件不存在，等待创建
[2025-07-16 20:06:40] 等待会话文件更新... 5秒
[2025-07-16 20:06:46] 等待会话文件更新... 10秒
[2025-07-16 20:06:51] 等待会话文件更新... 15秒
[2025-07-16 20:06:57] 等待会话文件更新... 20秒
[2025-07-16 20:07:02] 等待会话文件更新... 25秒
[2025-07-16 20:07:08] 等待会话文件更新... 30秒
[2025-07-16 20:07:08] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 20:07:08] SendToAI 返回，回复长度: 0
[2025-07-16 20:15:23] === GUI 程序启动 ===
[2025-07-16 20:15:23] 开始创建窗体
[2025-07-16 20:15:23] 设置窗体属性
[2025-07-16 20:15:23] 创建 GUI 控件
[2025-07-16 20:15:23] 开始创建 GUI 控件
[2025-07-16 20:15:23] 创建聊天显示区
[2025-07-16 20:15:23] GUI 控件创建完成
[2025-07-16 20:15:23] 创建初始化定时器
[2025-07-16 20:15:23] 初始化变量
[2025-07-16 20:15:23] 开始初始化会话
[2025-07-16 20:15:23] 开始初始化会话
[2025-07-16 20:15:23] 设置配置文件路径
[2025-07-16 20:15:23] 检查配置文件: fresh_session_config.yml
[2025-07-16 20:15:23] 检查 acli.exe
[2025-07-16 20:15:23] 找到 acli.exe 在上级目录
[2025-07-16 20:15:23] 读取配置文件中的会话目录设置
[2025-07-16 20:15:23] 使用会话目录: C:\test\chatsessiontest\fresh_sessions
[2025-07-16 20:15:23] 未找到任何会话目录
[2025-07-16 20:15:23] 会话初始化失败
[2025-07-16 20:15:23] 启动自动认证检查定时器
[2025-07-16 20:15:23] === GUI 程序启动完成 ===
[2025-07-16 20:15:23] === 窗体显示事件触发 ===
[2025-07-16 20:15:23] === 开始自动认证检查 ===
[2025-07-16 20:15:23] 调用 CheckAuthStatus
[2025-07-16 20:15:23] === 开始检查认证状态 ===
[2025-07-16 20:15:23] 设置 Process 参数
[2025-07-16 20:15:23] 设置 Process 选项
[2025-07-16 20:15:23] 开始执行认证状态检查
[2025-07-16 20:15:26] 认证状态检查完成，退出代码: 0
[2025-07-16 20:15:26] 获取到输出，行数: 1
[2025-07-16 20:15:26] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:26] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:26] 用户邮箱: 
[2025-07-16 20:15:26] 认证状态: 已认证
[2025-07-16 20:15:26] 是否已认证: True
[2025-07-16 20:15:26] === 更新窗体标题 ===
[2025-07-16 20:15:26] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:15:26] === 认证状态检查完成 ===
[2025-07-16 20:15:26] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:26] === 定时器触发，开始自动认证检查 ===
[2025-07-16 20:15:26] === 开始自动认证检查 ===
[2025-07-16 20:15:26] 调用 CheckAuthStatus
[2025-07-16 20:15:26] === 开始检查认证状态 ===
[2025-07-16 20:15:26] 设置 Process 参数
[2025-07-16 20:15:26] 设置 Process 选项
[2025-07-16 20:15:26] 开始执行认证状态检查
[2025-07-16 20:15:29] 认证状态检查完成，退出代码: 0
[2025-07-16 20:15:29] 获取到输出，行数: 1
[2025-07-16 20:15:29] 输出行 0: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:29] 认证状态检查结果: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:29] 用户邮箱: 
[2025-07-16 20:15:29] 认证状态: 已认证
[2025-07-16 20:15:29] 是否已认证: True
[2025-07-16 20:15:29] === 更新窗体标题 ===
[2025-07-16 20:15:29] 窗体标题已更新: RovoDev 对话界面 - ✅ 已认证
[2025-07-16 20:15:29] === 认证状态检查完成 ===
[2025-07-16 20:15:29] CheckAuthStatus 返回: ✓ Authenticated
 Email: <EMAIL>
 Token: ATAT************************
[2025-07-16 20:15:29] 认证状态：已认证
[2025-07-16 20:15:29] === 自动认证检查完成 ===
[2025-07-16 20:15:29] 认证状态：已认证
[2025-07-16 20:15:29] === 自动认证检查完成 ===
[2025-07-16 20:15:46] === 开始发送问题 ===
[2025-07-16 20:15:46] 用户问题: 测试
[2025-07-16 20:15:46] 禁用按钮和输入框
[2025-07-16 20:15:46] 聊天计数: 1
[2025-07-16 20:15:46] 开始调用 SendToAI
[2025-07-16 20:15:46] 进入 SendToAI 方法，问题: 测试
[2025-07-16 20:15:46] 显示发送状态
[2025-07-16 20:15:46] 创建 TProcess
[2025-07-16 20:15:46] 设置 Process 参数
[2025-07-16 20:15:46] 使用 acli.exe 路径: ..\acli.exe
[2025-07-16 20:15:46] 使用配置文件路径: C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:15:46] 添加用户问题参数: 测试
[2025-07-16 20:15:46] 设置 Process 选项
[2025-07-16 20:15:46] 设置工作目录: C:\test\chatsessiontest
[2025-07-16 20:15:46] 准备执行命令:
[2025-07-16 20:15:46]   可执行文件: ..\acli.exe
[2025-07-16 20:15:46]   工作目录: C:\test\chatsessiontest
[2025-07-16 20:15:46]   参数列表:
[2025-07-16 20:15:46]     [0] rovodev
[2025-07-16 20:15:46]     [1] run
[2025-07-16 20:15:46]     [2] --config-file
[2025-07-16 20:15:46]     [3] C:\test\chatsessiontest\fresh_session_config.yml
[2025-07-16 20:15:46]     [4] 测试
[2025-07-16 20:15:46] 完整命令: ..\acli.exe "rovodev" "run" "--config-file" "C:\test\chatsessiontest\fresh_session_config.yml" "测试"
[2025-07-16 20:15:46] 开始执行 Process (流式模式)
[2025-07-16 20:15:46] Process 启动成功，开始流式处理...
[2025-07-16 20:15:46] 流式进程已启动，进程ID: 13572
[2025-07-16 20:15:46] 流式进程已启动，进程ID: 13572
[2025-07-16 20:15:46] 开始等待会话文件更新...
[2025-07-16 20:15:46] 会话文件不存在，等待创建
[2025-07-16 20:15:52] 等待会话文件更新... 5秒
[2025-07-16 20:15:57] 等待会话文件更新... 10秒
[2025-07-16 20:16:03] 等待会话文件更新... 15秒
[2025-07-16 20:16:08] 等待会话文件更新... 20秒
[2025-07-16 20:16:14] 等待会话文件更新... 25秒
[2025-07-16 20:16:19] 等待会话文件更新... 30秒
[2025-07-16 20:16:19] ❌ 超时：30秒内未检测到会话文件更新
[2025-07-16 20:16:19] SendToAI 返回，回复长度: 0
